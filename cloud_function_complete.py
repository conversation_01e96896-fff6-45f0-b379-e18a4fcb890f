#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量重命名工具 - 云函数代码
部署到阿里云函数计算
"""

import json
import time
from datetime import datetime

# 阿里云表格存储配置
ENDPOINT = 'https://piliang-ots.cn-beijing.ots.aliyuncs.com'
ACCESS_KEY_ID = 'LTAI5tBKxKvKQhJQJKxKvKQh'
ACCESS_KEY_SECRET = 'your_access_key_secret_here'
INSTANCE_NAME = 'piliang-ots'

# 表名配置
TABLE_DEVICE_RECORDS = 'device_records'
TABLE_ACTIVATION_CODES = 'activation_codes'
TABLE_USAGE_LOGS = 'usage_logs'

# 业务配置
FREE_TRIAL_LIMIT = 3

# 导入阿里云表格存储SDK
try:
    from tablestore import OTSClient, Row, Condition, RowExistenceExpectation
    TABLESTORE_AVAILABLE = True
except ImportError as e:
    print(f"警告: tablestore SDK未安装: {e}")
    OTSClient = None
    Row = None
    Condition = None
    RowExistenceExpectation = None
    TABLESTORE_AVAILABLE = False

def get_ots_client():
    """获取表格存储客户端"""
    if not TABLESTORE_AVAILABLE:
        raise Exception("表格存储SDK不可用")
    
    return OTSClient(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET, INSTANCE_NAME)

def check_device_status(device_id, ip_address=None):
    """检查设备状态"""
    try:
        client = get_ots_client()
        current_time = datetime.now().isoformat()
        
        # 记录检查日志
        log_id = f"{device_id}_{int(time.time() * 1000)}"
        log_primary_key = [('log_id', log_id)]
        log_attribute_columns = [
            ('device_id', device_id),
            ('action', 'check'),
            ('timestamp', current_time),
            ('ip_address', ip_address or 'unknown')
        ]
        
        log_row = Row(log_primary_key, log_attribute_columns)
        client.put_row(TABLE_USAGE_LOGS, log_row)
        
        # 查询设备记录
        primary_key = [('device_id', device_id)]
        columns_to_get = ['use_count', 'is_activated', 'activation_code', 'first_use_time']

        consumed, return_row, next_token = client.get_row(
            TABLE_DEVICE_RECORDS, primary_key, columns_to_get
        )

        # return_row可能是对象或None，当记录存在时才有attribute_columns
        if return_row and hasattr(return_row, 'attribute_columns') and return_row.attribute_columns:
            # 设备已存在
            # 处理表格存储返回的属性列格式 (name, value, timestamp)
            attrs = {}
            for attr in return_row.attribute_columns:
                if len(attr) >= 2:
                    attrs[attr[0]] = attr[1]
            
            use_count = attrs.get('use_count', 0)
            is_activated = attrs.get('is_activated', False)
            activation_code = attrs.get('activation_code')
            
            # 更新最后检查时间
            update_primary_key = [('device_id', device_id)]
            update_attribute_columns = {'PUT': [('last_check_time', current_time)]}
            condition = Condition(RowExistenceExpectation.EXPECT_EXIST)

            update_row = Row(update_primary_key, update_attribute_columns)
            client.update_row(TABLE_DEVICE_RECORDS, update_row, condition)
            
            return {
                "device_exists": True,
                "use_count": use_count,
                "remaining_trials": max(0, FREE_TRIAL_LIMIT - use_count),
                "is_activated": bool(is_activated),
                "can_use": bool(is_activated) or use_count < FREE_TRIAL_LIMIT,
                "activation_code": activation_code if is_activated else None
            }
        else:
            # 新设备
            new_primary_key = [('device_id', device_id)]
            new_attribute_columns = [
                ('first_use_time', current_time),
                ('use_count', 0),
                ('is_activated', False),
                ('last_check_time', current_time),
                ('created_at', current_time)
            ]
            
            new_row = Row(new_primary_key, new_attribute_columns)
            client.put_row(TABLE_DEVICE_RECORDS, new_row)
            
            return {
                "device_exists": False,
                "use_count": 0,
                "remaining_trials": FREE_TRIAL_LIMIT,
                "is_activated": False,
                "can_use": True,
                "activation_code": None
            }
            
    except Exception as e:
        return {"error": str(e)}

def record_software_use(device_id, ip_address=None):
    """记录软件使用"""
    try:
        client = get_ots_client()
        current_time = datetime.now().isoformat()
        
        # 记录使用日志
        log_id = f"{device_id}_{int(time.time() * 1000)}"
        log_primary_key = [('log_id', log_id)]
        log_attribute_columns = [
            ('device_id', device_id),
            ('action', 'use'),
            ('timestamp', current_time),
            ('ip_address', ip_address or 'unknown')
        ]
        
        log_row = Row(log_primary_key, log_attribute_columns)
        client.put_row(TABLE_USAGE_LOGS, log_row)
        
        # 先获取当前使用次数
        primary_key = [('device_id', device_id)]
        columns_to_get = ['use_count', 'is_activated']
        
        consumed, return_row, next_token = client.get_row(
            TABLE_DEVICE_RECORDS, primary_key, columns_to_get
        )

        # return_row可能是对象或None，当记录存在时才有attribute_columns
        if return_row and hasattr(return_row, 'attribute_columns') and return_row.attribute_columns:
            # 处理表格存储返回的属性列格式
            attrs = {}
            for attr in return_row.attribute_columns:
                if len(attr) >= 2:
                    attrs[attr[0]] = attr[1]
            
            current_use_count = attrs.get('use_count', 0)
            is_activated = attrs.get('is_activated', False)
            
            # 更新使用次数
            new_use_count = current_use_count + 1
            update_primary_key = [('device_id', device_id)]
            update_attribute_columns = {
                'PUT': [
                    ('use_count', new_use_count),
                    ('last_check_time', current_time)
                ]
            }
            condition = Condition(RowExistenceExpectation.EXPECT_EXIST)

            update_row = Row(update_primary_key, update_attribute_columns)
            client.update_row(TABLE_DEVICE_RECORDS, update_row, condition)
            
            return {
                "success": True,
                "use_count": new_use_count,
                "remaining_trials": max(0, FREE_TRIAL_LIMIT - new_use_count),
                "is_activated": bool(is_activated)
            }
        else:
            return {"success": False, "message": "设备记录不存在"}
            
    except Exception as e:
        return {"success": False, "message": str(e)}

def activate_device(device_id, activation_code, ip_address=None):
    """激活设备"""
    try:
        client = get_ots_client()
        current_time = datetime.now().isoformat()

        # 检查激活码是否有效
        code_primary_key = [('code', activation_code)]
        code_columns_to_get = ['is_used', 'device_id']

        consumed, code_row, next_token = client.get_row(
            TABLE_ACTIVATION_CODES, code_primary_key, code_columns_to_get
        )

        # 检查激活码记录是否存在
        if not code_row or not hasattr(code_row, 'attribute_columns') or not code_row.attribute_columns:
            return {"success": False, "message": "激活码不存在"}

        # 处理表格存储返回的属性列格式
        code_attrs = {}
        for attr in code_row.attribute_columns:
            if len(attr) >= 2:
                code_attrs[attr[0]] = attr[1]

        is_used = code_attrs.get('is_used', False)
        bound_device_id = code_attrs.get('device_id')

        if is_used and bound_device_id != device_id:
            return {"success": False, "message": "激活码已被其他设备使用"}

        if is_used and bound_device_id == device_id:
            return {"success": True, "message": "设备已激活"}

        # 检查设备是否已经激活过其他激活码
        device_primary_key = [('device_id', device_id)]
        device_columns_to_get = ['is_activated', 'activation_code']

        consumed, device_row, next_token = client.get_row(
            TABLE_DEVICE_RECORDS, device_primary_key, device_columns_to_get
        )

        # 检查设备记录是否存在
        if device_row and hasattr(device_row, 'attribute_columns') and device_row.attribute_columns:
            # 处理表格存储返回的属性列格式
            device_attrs = {}
            for attr in device_row.attribute_columns:
                if len(attr) >= 2:
                    device_attrs[attr[0]] = attr[1]

            if device_attrs.get('is_activated', False):
                return {"success": False, "message": "设备已激活过其他激活码"}

        # 执行激活 - 更新激活码状态
        code_update_primary_key = [('code', activation_code)]
        code_update_attributes = {
            'PUT': [
                ('is_used', True),
                ('device_id', device_id),
                ('used_time', current_time)
            ]
        }
        code_condition = Condition(RowExistenceExpectation.EXPECT_EXIST)

        code_update_row = Row(code_update_primary_key, code_update_attributes)
        client.update_row(TABLE_ACTIVATION_CODES, code_update_row, code_condition)

        # 更新设备状态
        device_update_primary_key = [('device_id', device_id)]
        device_update_attributes = {
            'PUT': [
                ('is_activated', True),
                ('activation_code', activation_code),
                ('last_check_time', current_time)
            ]
        }
        device_condition = Condition(RowExistenceExpectation.EXPECT_EXIST)

        device_update_row = Row(device_update_primary_key, device_update_attributes)
        client.update_row(TABLE_DEVICE_RECORDS, device_update_row, device_condition)

        # 记录激活日志
        log_id = f"{device_id}_{int(time.time() * 1000)}"
        log_primary_key = [('log_id', log_id)]
        log_attribute_columns = [
            ('device_id', device_id),
            ('action', 'activate'),
            ('timestamp', current_time),
            ('ip_address', ip_address or 'unknown')
        ]

        log_row = Row(log_primary_key, log_attribute_columns)
        client.put_row(TABLE_USAGE_LOGS, log_row)

        return {"success": True, "message": "激活成功"}

    except Exception as e:
        return {"success": False, "message": f"激活失败: {str(e)}"}

def add_activation_codes(codes, admin_key):
    """添加激活码（管理员功能）"""
    if admin_key != '010bhqlijia1':
        return {"success": False, "message": "权限不足"}

    try:
        client = get_ots_client()
        current_time = datetime.now().isoformat()
        added_count = 0

        for code in codes:
            try:
                # 检查激活码是否已存在
                primary_key = [('code', code)]
                consumed, return_row, next_token = client.get_row(TABLE_ACTIVATION_CODES, primary_key, [])

                # 检查激活码是否已存在：如果return_row为空或者没有属性列，说明记录不存在
                record_exists = (return_row and
                               hasattr(return_row, 'attribute_columns') and
                               return_row.attribute_columns)

                if not record_exists:
                    # 激活码不存在，可以添加
                    code_primary_key = [('code', code)]
                    code_attribute_columns = [
                        ('is_used', False),
                        ('created_at', current_time)
                    ]

                    code_row = Row(code_primary_key, code_attribute_columns)
                    client.put_row(TABLE_ACTIVATION_CODES, code_row)
                    added_count += 1
                # 如果已存在则跳过

            except Exception as e:
                print(f"添加激活码 {code} 失败: {e}")

        return {
            "success": True,
            "message": f"成功添加 {added_count} 个激活码，跳过 {len(codes) - added_count} 个重复激活码",
            "added_count": added_count,
            "total_count": len(codes)
        }

    except Exception as e:
        return {"success": False, "message": str(e)}

def handler(environ, start_response):
    """主处理函数"""
    try:
        # 解析请求
        method = environ.get('REQUEST_METHOD', 'GET')
        path = environ.get('PATH_INFO', '/')

        # 读取请求体
        content_length = int(environ.get('CONTENT_LENGTH', 0))
        if content_length > 0:
            request_body = environ['wsgi.input'].read(content_length)
            try:
                data = json.loads(request_body.decode('utf-8'))
            except:
                data = {}
        else:
            data = {}

        # 获取客户端IP
        ip_address = environ.get('HTTP_X_FORWARDED_FOR', environ.get('REMOTE_ADDR'))

        # 路由处理
        if path == '/check' and method == 'POST':
            device_id = data.get('device_id')
            if not device_id:
                result = {'error': '缺少设备ID'}
                status = '400 Bad Request'
            else:
                result = check_device_status(device_id, ip_address)
                status = '200 OK'

        elif path == '/use' and method == 'POST':
            device_id = data.get('device_id')
            if not device_id:
                result = {'error': '缺少设备ID'}
                status = '400 Bad Request'
            else:
                result = record_software_use(device_id, ip_address)
                status = '200 OK'

        elif path == '/activate' and method == 'POST':
            device_id = data.get('device_id')
            activation_code = data.get('activation_code')
            if not device_id or not activation_code:
                result = {'error': '缺少必要参数'}
                status = '400 Bad Request'
            else:
                result = activate_device(device_id, activation_code, ip_address)
                status = '200 OK'

        elif path == '/admin/add_codes' and method == 'POST':
            codes = data.get('codes', [])
            admin_key = data.get('admin_key')
            result = add_activation_codes(codes, admin_key)
            status = '200 OK'

        else:
            result = {'error': '接口不存在'}
            status = '404 Not Found'

        # 返回响应
        response_body = json.dumps(result, ensure_ascii=False)
        response_headers = [
            ('Content-Type', 'application/json; charset=utf-8'),
            ('Content-Length', str(len(response_body.encode('utf-8')))),
            ('Access-Control-Allow-Origin', '*'),
            ('Access-Control-Allow-Methods', 'GET, POST, OPTIONS'),
            ('Access-Control-Allow-Headers', 'Content-Type')
        ]

        start_response(status, response_headers)
        return [response_body.encode('utf-8')]

    except Exception as e:
        error_response = json.dumps({'error': f'服务器内部错误: {str(e)}'}, ensure_ascii=False)
        start_response('500 Internal Server Error', [
            ('Content-Type', 'application/json; charset=utf-8'),
            ('Content-Length', str(len(error_response.encode('utf-8'))))
        ])
        return [error_response.encode('utf-8')]
