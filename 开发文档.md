# 批量文件重命名工具开发文档

## 项目概述
开发一个基于Python的GUI批量文件重命名工具，支持多种命名方式，适用于所有文件类型。

## 功能需求

### 核心功能
- 批量选择文件进行重命名
- 支持所有文件类型（只修改文件名，不读取内容）
- 提供直观的GUI界面
- 支持预览重命名结果

### 三种命名方式

#### 1. 用户自定义命名
- 导入包含名称列表的文件（支持txt、csv格式）
- 按顺序为选中文件分配名称
- 支持名称不足时的处理策略

#### 2. 特定内容读取命名
- **Word文档**：读取指定段落或位置的文本内容
- **Excel文件**：读取指定单元格的内容
- 将读取的内容作为该文件的新名称
- 仅对Word和Excel文件有效

#### 3. 规律命名
- **数字序列**：1,2,3... 或 001,002,003...
- **字母序列**：A,B,C... 或 a,b,c...
- **日期序列**：基于当前日期或指定日期的序列
- **自定义前缀/后缀**：支持添加前缀和后缀

## 技术架构

### 主要模块
1. **main.py** - 主启动文件
2. **gui_module.py** - GUI界面模块
3. **rename_engine.py** - 重命名核心逻辑
4. **file_reader.py** - 文件内容读取模块
5. **pattern_generator.py** - 规律命名生成器

### 技术栈
- **GUI框架**：tkinter（Python内置，无需额外安装）
- **文件处理**：
  - Word文档：python-docx
  - Excel文件：openpyxl
  - 通用文件操作：os, pathlib
- **界面组件**：tkinter.ttk（现代化界面）

## 界面设计

### 主窗口布局
```
┌─────────────────────────────────────────┐
│ 批量文件重命名工具                        │
├─────────────────────────────────────────┤
│ [选择文件] [选择文件夹] [清空列表]         │
├─────────────────────────────────────────┤
│ 文件列表区域                             │
│ ┌─────────────────────────────────────┐ │
│ │ 原文件名    │ 新文件名    │ 状态    │ │
│ │ file1.txt   │ 新名称1     │ 待处理  │ │
│ │ file2.jpg   │ 新名称2     │ 待处理  │ │
│ └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│ 命名方式选择：                           │
│ ○ 用户自定义  ○ 内容读取  ○ 规律命名    │
├─────────────────────────────────────────┤
│ 配置区域（根据选择的命名方式动态变化）     │
├─────────────────────────────────────────┤
│ [预览] [执行重命名] [撤销] [退出]         │
└─────────────────────────────────────────┘
```

### 配置区域详细设计

#### 用户自定义模式
- 文件选择按钮：选择包含名称的txt/csv文件
- 名称列表预览
- 处理策略选择（名称不足时：跳过/使用原名/添加序号）

#### 内容读取模式
- 仅对Word和Excel文件启用
- Word配置：段落位置选择
- Excel配置：单元格位置输入（如A1, B2）
- 内容预览区域

#### 规律命名模式
- 序列类型选择：数字/字母/日期
- 起始值设置
- 格式选项（补零位数、大小写等）
- 前缀/后缀输入框

## 开发计划

### 第一阶段：基础框架
1. 创建主启动文件和基本GUI框架
2. 实现文件选择和列表显示功能
3. 建立模块间的基本通信机制

### 第二阶段：核心功能
1. 实现规律命名功能（最简单）
2. 实现用户自定义命名功能
3. 实现基本的重命名执行逻辑

### 第三阶段：高级功能
1. 实现Word/Excel内容读取功能
2. 添加预览和撤销功能
3. 完善错误处理和用户体验

### 第四阶段：优化完善
1. 界面美化和用户体验优化
2. 添加配置保存功能
3. 完善文档和使用说明

## 注意事项
- 严格遵循开发原则：只有一个.py启动文件
- 不创建测试文件，在对话中进行测试
- 确保所有文件类型都能正确处理
- 提供友好的错误提示和用户指导
