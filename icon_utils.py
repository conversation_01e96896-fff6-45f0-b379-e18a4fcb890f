#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标工具模块 - 处理程序图标的加载和设置
"""

import os
import sys
from pathlib import Path

def get_icon_path():
    """获取图标文件路径"""
    try:
        # 如果是打包后的exe文件
        if getattr(sys, 'frozen', False):
            # PyInstaller打包后的临时目录
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(os.path.abspath(__file__))
        
        icon_path = os.path.join(base_path, "icon.ico")
        
        # 检查图标文件是否存在
        if os.path.exists(icon_path):
            return icon_path
        else:
            print(f"警告: 图标文件不存在: {icon_path}")
            return None
            
    except Exception as e:
        print(f"获取图标路径失败: {e}")
        return None

def set_window_icon(window):
    """为窗口设置图标"""
    try:
        icon_path = get_icon_path()
        if icon_path:
            window.iconbitmap(icon_path)
            return True
        else:
            print("警告: 无法设置窗口图标，图标文件不存在")
            return False
    except Exception as e:
        print(f"设置窗口图标失败: {e}")
        return False

def set_window_icon_safe(window):
    """安全地为窗口设置图标（忽略错误）"""
    try:
        return set_window_icon(window)
    except:
        # 静默忽略所有错误
        return False
