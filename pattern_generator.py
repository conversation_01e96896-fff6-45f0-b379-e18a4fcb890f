#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规律命名生成器模块 - 生成各种规律的文件名
"""

from datetime import datetime, timedelta
import calendar

class PatternGenerator:
    """规律命名生成器"""
    
    def __init__(self):
        pass
    
    def generate_number_sequence(self, count, start=1, padding=0, prefix="", suffix=""):
        """
        生成数字序列
        
        Args:
            count: 生成数量
            start: 起始数字
            padding: 补零位数（0表示不补零）
            prefix: 前缀
            suffix: 后缀
        
        Returns:
            list: 数字序列列表
        """
        sequence = []
        for i in range(count):
            number = start + i
            if padding > 0:
                number_str = str(number).zfill(padding)
            else:
                number_str = str(number)
            
            name = f"{prefix}{number_str}{suffix}"
            sequence.append(name)
        
        return sequence
    
    def generate_letter_sequence(self, count, start='A', case='upper', prefix="", suffix=""):
        """
        生成字母序列
        
        Args:
            count: 生成数量
            start: 起始字母
            case: 大小写 ('upper', 'lower')
            prefix: 前缀
            suffix: 后缀
        
        Returns:
            list: 字母序列列表
        """
        sequence = []
        start_char = start.upper()
        start_ord = ord(start_char)
        
        for i in range(count):
            current_ord = start_ord + i
            
            # 处理超过Z的情况
            if current_ord <= ord('Z'):
                letter = chr(current_ord)
            else:
                # 使用AA, AB, AC...的方式
                extra_cycles = (current_ord - ord('A')) // 26
                remainder = (current_ord - ord('A')) % 26
                
                if extra_cycles == 1:
                    letter = 'A' + chr(ord('A') + remainder)
                else:
                    # 更复杂的情况，如AAA, AAB等
                    letter = self._generate_multi_letter(current_ord - ord('A'))
            
            if case == 'lower':
                letter = letter.lower()
            
            name = f"{prefix}{letter}{suffix}"
            sequence.append(name)
        
        return sequence
    
    def _generate_multi_letter(self, index):
        """
        生成多字母组合（如AA, AB, AAA等）
        
        Args:
            index: 索引值
        
        Returns:
            str: 字母组合
        """
        result = ""
        index += 1  # 转换为1基索引
        
        while index > 0:
            index -= 1
            result = chr(ord('A') + index % 26) + result
            index //= 26
        
        return result
    
    def generate_date_sequence(self, count, start_date=None, date_format="%Y%m%d",
                              interval_days=1, prefix="", suffix=""):
        """
        生成日期序列

        Args:
            count: 生成数量
            start_date: 起始日期（datetime对象或字符串，格式为YYYY-MM-DD）
            date_format: 日期格式
            interval_days: 日期间隔天数
            prefix: 前缀
            suffix: 后缀

        Returns:
            list: 日期序列列表
        """
        sequence = []

        # 处理起始日期
        if start_date is None:
            base_date = datetime.now()
        elif isinstance(start_date, str):
            # 尝试多种日期格式
            date_formats = ["%Y-%m-%d", "%Y/%m/%d", "%Y.%m.%d", "%Y%m%d"]
            base_date = None

            for fmt in date_formats:
                try:
                    base_date = datetime.strptime(start_date.strip(), fmt)
                    break
                except ValueError:
                    continue

            # 如果所有格式都失败，使用当前日期
            if base_date is None:
                print(f"警告：无法解析日期 '{start_date}'，使用当前日期")
                base_date = datetime.now()
        else:
            base_date = start_date

        # 生成日期序列（Python的timedelta会自动处理月份天数差异）
        for i in range(count):
            current_date = base_date + timedelta(days=i * interval_days)
            date_str = current_date.strftime(date_format)
            name = f"{prefix}{date_str}{suffix}"
            sequence.append(name)

        return sequence
    
    def generate_time_sequence(self, count, start_time=None, time_format="%H%M%S", 
                              interval_seconds=1, prefix="", suffix=""):
        """
        生成时间序列
        
        Args:
            count: 生成数量
            start_time: 起始时间
            time_format: 时间格式
            interval_seconds: 时间间隔秒数
            prefix: 前缀
            suffix: 后缀
        
        Returns:
            list: 时间序列列表
        """
        sequence = []
        
        if start_time is None:
            base_time = datetime.now()
        else:
            base_time = start_time
        
        for i in range(count):
            current_time = base_time + timedelta(seconds=i * interval_seconds)
            time_str = current_time.strftime(time_format)
            name = f"{prefix}{time_str}{suffix}"
            sequence.append(name)
        
        return sequence
    
    def generate_custom_pattern(self, count, pattern, start_index=0):
        """
        生成自定义模式序列
        
        Args:
            count: 生成数量
            pattern: 模式字符串，支持占位符：
                    {n} - 数字
                    {N} - 补零数字
                    {a} - 小写字母
                    {A} - 大写字母
                    {d} - 日期
                    {t} - 时间
            start_index: 起始索引
        
        Returns:
            list: 自定义模式序列列表
        """
        sequence = []
        base_date = datetime.now()
        
        for i in range(count):
            current_index = start_index + i
            name = pattern
            
            # 替换数字占位符
            name = name.replace('{n}', str(current_index))
            name = name.replace('{N}', str(current_index).zfill(3))
            
            # 替换字母占位符
            if current_index < 26:
                name = name.replace('{a}', chr(ord('a') + current_index))
                name = name.replace('{A}', chr(ord('A') + current_index))
            else:
                name = name.replace('{a}', self._generate_multi_letter(current_index).lower())
                name = name.replace('{A}', self._generate_multi_letter(current_index))
            
            # 替换日期时间占位符
            current_date = base_date + timedelta(days=i)
            name = name.replace('{d}', current_date.strftime("%Y%m%d"))
            name = name.replace('{t}', current_date.strftime("%H%M%S"))
            
            sequence.append(name)
        
        return sequence
    
    def generate_random_sequence(self, count, length=8, charset='alphanumeric', prefix="", suffix=""):
        """
        生成随机字符序列
        
        Args:
            count: 生成数量
            length: 随机字符长度
            charset: 字符集类型 ('alphanumeric', 'letters', 'numbers', 'hex')
            prefix: 前缀
            suffix: 后缀
        
        Returns:
            list: 随机序列列表
        """
        import random
        import string
        
        sequence = []
        
        # 定义字符集
        if charset == 'alphanumeric':
            chars = string.ascii_letters + string.digits
        elif charset == 'letters':
            chars = string.ascii_letters
        elif charset == 'numbers':
            chars = string.digits
        elif charset == 'hex':
            chars = string.hexdigits.lower()
        else:
            chars = string.ascii_letters + string.digits
        
        for _ in range(count):
            random_str = ''.join(random.choice(chars) for _ in range(length))
            name = f"{prefix}{random_str}{suffix}"
            sequence.append(name)
        
        return sequence
    
    def get_preview_samples(self, pattern_type, config, sample_count=5):
        """
        获取模式预览样本
        
        Args:
            pattern_type: 模式类型
            config: 配置字典
            sample_count: 样本数量
        
        Returns:
            list: 预览样本列表
        """
        if pattern_type == 'number':
            return self.generate_number_sequence(
                sample_count,
                config.get('start', 1),
                config.get('padding', 0),
                config.get('prefix', ''),
                config.get('suffix', '')
            )
        elif pattern_type == 'letter':
            return self.generate_letter_sequence(
                sample_count,
                config.get('start', 'A'),
                config.get('case', 'upper'),
                config.get('prefix', ''),
                config.get('suffix', '')
            )
        elif pattern_type == 'date':
            return self.generate_date_sequence(
                sample_count,
                config.get('start_date'),
                config.get('date_format', '%Y%m%d'),
                config.get('interval_days', 1),
                config.get('prefix', ''),
                config.get('suffix', '')
            )
        elif pattern_type == 'custom':
            return self.generate_custom_pattern(
                sample_count,
                config.get('pattern', '{N}'),
                config.get('start_index', 0)
            )
        elif pattern_type == 'random':
            return self.generate_random_sequence(
                sample_count,
                config.get('length', 8),
                config.get('charset', 'alphanumeric'),
                config.get('prefix', ''),
                config.get('suffix', '')
            )
        else:
            return []
    
    def validate_config(self, pattern_type, config):
        """
        验证配置参数
        
        Args:
            pattern_type: 模式类型
            config: 配置字典
        
        Returns:
            tuple: (是否有效, 错误消息)
        """
        if pattern_type == 'number':
            start = config.get('start', 1)
            if not isinstance(start, int) or start < 0:
                return False, "起始数字必须是非负整数"
            
            padding = config.get('padding', 0)
            if not isinstance(padding, int) or padding < 0:
                return False, "补零位数必须是非负整数"
        
        elif pattern_type == 'letter':
            start = config.get('start', 'A')
            if not isinstance(start, str) or len(start) != 1 or not start.isalpha():
                return False, "起始字母必须是单个字母"
        
        elif pattern_type == 'date':
            start_date = config.get('start_date')
            if start_date and isinstance(start_date, str):
                try:
                    datetime.strptime(start_date, "%Y-%m-%d")
                except ValueError:
                    return False, "日期格式必须是YYYY-MM-DD"
        
        return True, ""
