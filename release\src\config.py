#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户端配置文件 - 只包含用户需要的配置信息
注意：此文件会打包到exe中，不要包含敏感信息！
"""

# 云函数API配置
# 部署云函数后，将下面的URL替换为实际的API网关地址
CLOUD_FUNCTION_URL = "https://piliangmingming-azgiihrxxg.cn-beijing.fcapp.run"

# 软件配置
SOFTWARE_NAME = "批量文件重命名工具"
SOFTWARE_VERSION = "1.0"

# 支付页面配置
PAYMENT_PAGE_URL = "https://username.gitee.io/yourapp/payment.html"
QQ_GROUP_URL = "https://qm.qq.com/cgi-bin/qm/qr?k=your_qq_group_key"
EMAIL_CONTACT = "<EMAIL>"
OFFICIAL_WEBSITE = "https://yourapp.com"

# 网络配置
REQUEST_TIMEOUT = 10  # 网络请求超时时间（秒）
CACHE_EXPIRE_DAYS = 7  # 本地缓存过期时间（天）

# 重试配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 1  # 初始重试延迟（秒）
BACKOFF_FACTOR = 1.5  # 退避因子（每次重试延迟递增）

# 注意：管理员密钥等敏感信息不包含在此文件中！
