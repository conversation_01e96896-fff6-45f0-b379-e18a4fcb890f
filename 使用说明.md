# 批量文件重命名工具使用说明

## 简介
这是一个基于Python开发的批量文件重命名工具，支持三种命名方式：规律命名、用户自定义命名和内容读取命名。

## 安装依赖
在运行程序前，请先安装必要的依赖包：

```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install python-docx openpyxl
```

## 启动程序
双击运行 `main.py` 文件，或在命令行中执行：
```bash
python main.py
```

## 功能说明

### 1. 文件选择
- **选择文件**：可以选择多个单独的文件进行重命名
- **选择文件夹**：选择文件夹后会自动添加文件夹内的所有文件
- **清空列表**：清除当前选中的所有文件

### 2. 命名方式

#### 规律命名
支持多种规律模式：
- **数字序列**：1, 2, 3... 或 001, 002, 003...
- **字母序列**：A, B, C... 或 a, b, c...
- **日期序列**：基于日期的序列命名

配置选项：
- **起始值**：设置序列的起始值
- **前缀**：在生成的名称前添加固定前缀
- **后缀**：在生成的名称后添加固定后缀

#### 用户自定义命名
从外部文件导入预设的名称列表：
- 支持多种文件格式：
  - `.txt` 文件：每行一个名称
  - `.csv` 文件：读取第一列作为名称
  - `.xlsx/.xls` 文件：读取第一列作为名称
- 按顺序为选中的文件分配名称
- 界面会显示支持的文件格式提示

#### 内容读取命名
仅适用于Word和Excel文件：
- **Word文档**：读取指定段落的内容作为文件名
- **Excel文件**：读取指定单元格的内容作为文件名

配置选项：
- **Word段落位置**：指定要读取的段落编号（从1开始）
- **Excel单元格**：指定要读取的单元格位置（如A1, B2）

### 3. 操作流程
1. 选择要重命名的文件
2. 选择命名方式并配置相关参数
3. 点击"预览重命名"查看重命名效果
4. 确认无误后点击"执行重命名"
5. 如需撤销，可点击"撤销操作"

## 注意事项

### 安全提示
- 重命名操作会直接修改文件名，请谨慎操作
- 建议先使用"预览重命名"功能确认效果
- 程序提供撤销功能，但建议重要文件先备份

### 文件名限制
- 文件名不能包含以下字符：`< > : " / \ | ? *`
- 程序会自动将非法字符替换为下划线
- 文件名长度限制为200个字符

### 依赖要求
- Python 3.6 或更高版本
- python-docx（处理Word文档）
- openpyxl（处理Excel文件）
- tkinter（GUI界面，Python内置）

## 常见问题

### Q: 程序无法启动？
A: 请检查是否安装了Python和必要的依赖包。

### Q: Word/Excel文件无法读取内容？
A: 请确保安装了 `python-docx` 和 `openpyxl` 库。

### Q: 重命名后文件无法打开？
A: 请检查新文件名是否包含非法字符，程序会自动处理但可能影响某些特殊文件。

### Q: 如何撤销重命名操作？
A: 点击"撤销操作"按钮可以撤销最近一次的重命名操作。

## 技术支持
如遇到问题，请检查：
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 文件权限是否足够
4. 文件是否被其他程序占用

## 版本信息
- 版本：1.0
- 开发语言：Python 3
- GUI框架：tkinter
- 支持平台：Windows, macOS, Linux
