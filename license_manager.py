#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证管理模块 - 处理软件试用和激活验证
"""

import hashlib
import platform
import subprocess
import uuid
import requests
import json
import os
from datetime import datetime
import time

class LicenseManager:
    """许可证管理器"""

    def __init__(self):
        try:
            from config import (CLOUD_FUNCTION_URL, REQUEST_TIMEOUT,
                              MAX_RETRIES, RETRY_DELAY, BACKOFF_FACTOR)
            self.api_url = CLOUD_FUNCTION_URL
            self.timeout = REQUEST_TIMEOUT
            self.max_retries = MAX_RETRIES
            self.retry_delay = RETRY_DELAY
            self.backoff_factor = BACKOFF_FACTOR
        except ImportError:
            # 如果配置文件不存在，使用默认值
            self.api_url = "https://service-xxx.gz.apigw.tencentcs.com/release"
            self.timeout = 10
            self.max_retries = 3
            self.retry_delay = 1
            self.backoff_factor = 1.5

        self.device_id = self.get_device_fingerprint()
        self.local_cache_file = self._get_cache_file_path()

    def _get_cache_file_path(self):
        """获取缓存文件路径"""
        try:
            # 获取当前用户名
            username = os.environ.get('USERNAME', 'DefaultUser')

            # 构建缓存目录路径
            cache_dir = f"C:\\Users\\<USER>\\AppData\\Local\\PLMM"

            # 确保目录存在
            os.makedirs(cache_dir, exist_ok=True)

            # 返回完整的缓存文件路径
            cache_file_path = os.path.join(cache_dir, "license_cache.dat")

            return cache_file_path

        except Exception as e:
            # 如果出现错误，回退到当前目录
            print(f"警告: 无法创建用户缓存目录，使用当前目录: {e}")
            return "license_cache.dat"

    def api_request_with_retry(self, endpoint, data, operation_name="API请求"):
        """带重试机制的API请求"""
        for attempt in range(self.max_retries + 1):
            try:
                print(f"{operation_name}中..." if attempt == 0 else f"{operation_name}重试中 ({attempt}/{self.max_retries})...")

                response = requests.post(
                    f"{self.api_url}{endpoint}",
                    json=data,
                    timeout=self.timeout,
                    headers={'Content-Type': 'application/json'}
                )

                if response.status_code == 200:
                    return True, response.json()
                else:
                    raise requests.RequestException(f"HTTP {response.status_code}")

            except (requests.RequestException, requests.Timeout, requests.ConnectionError) as e:
                if attempt == self.max_retries:
                    print(f"{operation_name}失败，已重试{self.max_retries}次: {str(e)}")
                    return False, {"error": f"网络请求失败: {str(e)}"}

                # 计算重试延迟（指数退避）
                delay = self.retry_delay * (self.backoff_factor ** attempt)
                print(f"网络不稳定，{delay:.1f}秒后重试...")
                time.sleep(delay)

        return False, {"error": "网络请求失败"}
    
    def get_device_fingerprint(self):
        """生成设备唯一指纹"""
        fingerprint_data = []
        
        try:
            if platform.system() == "Windows":
                # CPU信息
                try:
                    cpu_info = subprocess.check_output("wmic cpu get ProcessorId", shell=True).decode()
                    fingerprint_data.append(cpu_info.strip())
                except:
                    pass
                
                # 主板序列号
                try:
                    motherboard = subprocess.check_output("wmic baseboard get serialnumber", shell=True).decode()
                    fingerprint_data.append(motherboard.strip())
                except:
                    pass
            
            # MAC地址
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> i) & 0xff) for i in range(0,8*6,8)][::-1])
            fingerprint_data.append(mac)
            
        except:
            pass
        
        # 备用方案
        if not fingerprint_data:
            fingerprint_data.append(platform.node())
            fingerprint_data.append(str(uuid.getnode()))
            fingerprint_data.append(platform.system())
        
        # 生成最终指纹
        combined = ''.join(fingerprint_data)
        return hashlib.sha256(combined.encode()).hexdigest()
    
    def check_license_status(self):
        """检查许可证状态"""
        data = {"device_id": self.device_id}

        success, result = self.api_request_with_retry("/check", data, "检查许可证状态")

        if success:
            self.save_local_cache(result)
            return True, result
        else:
            # 网络错误，使用本地缓存
            print("网络请求失败，使用本地缓存...")
            cached_result = self.load_local_cache()
            return False, cached_result
    
    def record_software_usage(self):
        """记录软件使用"""
        data = {"device_id": self.device_id}

        success, result = self.api_request_with_retry("/use", data, "记录软件使用")

        if success and result.get("success"):
            self.save_local_cache(result)
            return True, result
        elif success:
            return True, result
        else:
            return False, {"success": False, "error": result.get("error", "网络错误")}
    
    def activate_software(self, activation_code):
        """激活软件"""
        data = {
            "device_id": self.device_id,
            "activation_code": activation_code.strip().upper()
        }

        success, result = self.api_request_with_retry("/activate", data, "激活软件")

        if success and result.get("success"):
            # 激活成功，保存到本地缓存
            cache_data = {
                "is_activated": True,
                "activation_code": activation_code,
                "can_use": True
            }
            self.save_local_cache(cache_data)
            return True, result
        elif success:
            return True, result
        else:
            return False, {"success": False, "message": result.get("error", "网络错误")}
    
    def save_local_cache(self, data):
        """保存本地缓存"""
        try:
            cache_data = {
                "device_id": self.device_id,
                "data": data,
                "timestamp": time.time()
            }
            
            # 简单加密存储
            cache_json = json.dumps(cache_data)
            encrypted_data = self.simple_encrypt(cache_json)
            
            with open(self.local_cache_file, 'wb') as f:
                f.write(encrypted_data)
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def load_local_cache(self):
        """加载本地缓存"""
        try:
            if os.path.exists(self.local_cache_file):
                with open(self.local_cache_file, 'rb') as f:
                    encrypted_data = f.read()
                
                # 解密
                cache_json = self.simple_decrypt(encrypted_data)
                cache_data = json.loads(cache_json)
                
                # 验证设备ID
                if cache_data.get("device_id") == self.device_id:
                    # 检查缓存是否过期（7天）
                    cache_time = cache_data.get("timestamp", 0)
                    if time.time() - cache_time < 7 * 24 * 3600:
                        return cache_data.get("data", {})
        except Exception as e:
            print(f"加载缓存失败: {e}")
        
        return {"can_use": False, "error": "无法验证许可证", "is_activated": False}
    
    def simple_encrypt(self, data):
        """简单加密"""
        key = hashlib.md5(self.device_id.encode()).digest()
        encrypted = bytearray()
        
        for i, byte in enumerate(data.encode('utf-8')):
            encrypted.append(byte ^ key[i % len(key)])
        
        return bytes(encrypted)
    
    def simple_decrypt(self, encrypted_data):
        """简单解密"""
        key = hashlib.md5(self.device_id.encode()).digest()
        decrypted = bytearray()
        
        for i, byte in enumerate(encrypted_data):
            decrypted.append(byte ^ key[i % len(key)])
        
        return decrypted.decode('utf-8')
    
    def can_use_software(self):
        """检查是否可以使用软件"""
        online, status = self.check_license_status()
        
        # 如果在线检查成功，使用在线结果
        if online:
            return status.get("can_use", False), status
        
        # 离线模式，使用缓存
        if status.get("is_activated"):
            return True, status
        
        # 未激活且无法在线验证
        return False, status
    
    def get_status_message(self, status):
        """获取状态消息"""
        if status.get("error"):
            return f"验证失败: {status['error']}"
        
        if status.get("is_activated"):
            return "软件已激活"
        
        remaining = status.get("remaining_trials", 0)
        if remaining > 0:
            return f"试用版本，剩余 {remaining} 次使用机会"
        else:
            return "试用次数已用完，请激活软件"
    
    def is_first_run(self):
        """检查是否首次运行"""
        return not os.path.exists(self.local_cache_file)
