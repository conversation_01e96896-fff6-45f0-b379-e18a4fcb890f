#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件内容读取模块 - 读取Word和Excel文件的特定内容
"""

import os
from pathlib import Path

class FileContentReader:
    """文件内容读取器"""
    
    def __init__(self):
        self.supported_word_extensions = ['.docx', '.doc']
        self.supported_excel_extensions = ['.xlsx', '.xls']
    
    def read_file_content(self, file_path, word_paragraph=1, excel_cell='A1',
                         word_search_text=None, word_char_count=None):
        """
        读取文件内容

        Args:
            file_path: 文件路径
            word_paragraph: Word文档段落位置（从1开始）
            excel_cell: Excel单元格位置（如A1）
            word_search_text: Word高级功能-搜索文本
            word_char_count: Word高级功能-提取字符数

        Returns:
            str: 读取到的内容，如果失败返回None
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()

        if extension in self.supported_word_extensions:
            if word_search_text and word_char_count:
                return self.read_word_content_advanced(file_path, word_search_text, word_char_count)
            else:
                return self.read_word_content(file_path, word_paragraph)
        elif extension in self.supported_excel_extensions:
            return self.read_excel_content(file_path, excel_cell)
        else:
            raise ValueError(f"不支持的文件类型: {extension}")
    
    def read_word_content(self, file_path, paragraph_index=1):
        """
        读取Word文档指定段落的内容
        
        Args:
            file_path: Word文件路径
            paragraph_index: 段落索引（从1开始）
        
        Returns:
            str: 段落内容
        """
        try:
            # 尝试导入python-docx
            try:
                from docx import Document
            except ImportError:
                raise ImportError("需要安装python-docx库: pip install python-docx")
            
            # 读取文档
            doc = Document(file_path)
            
            # 检查段落索引是否有效
            if paragraph_index < 1 or paragraph_index > len(doc.paragraphs):
                raise IndexError(f"段落索引 {paragraph_index} 超出范围 (1-{len(doc.paragraphs)})")
            
            # 获取指定段落的文本（索引从0开始，所以减1）
            paragraph = doc.paragraphs[paragraph_index - 1]
            content = paragraph.text.strip()
            
            if not content:
                raise ValueError(f"第 {paragraph_index} 段落为空")
            
            return content
        
        except Exception as e:
            raise Exception(f"读取Word文档失败: {str(e)}")

    def read_word_content_advanced(self, file_path, search_text, char_count):
        """
        Word高级功能：搜索指定文本并提取后面的字符

        Args:
            file_path: Word文件路径
            search_text: 要搜索的文本
            char_count: 要提取的字符数量

        Returns:
            str: 提取的内容
        """
        try:
            # 尝试导入python-docx
            try:
                from docx import Document
            except ImportError:
                raise ImportError("需要安装python-docx库: pip install python-docx")

            # 读取文档
            doc = Document(file_path)

            # 在所有段落中搜索目标文本
            full_text = ""
            for paragraph in doc.paragraphs:
                full_text += paragraph.text + "\n"

            # 查找搜索文本的位置
            search_index = full_text.find(search_text)
            if search_index == -1:
                raise ValueError(f"在文档中未找到搜索文本: '{search_text}'")

            # 计算提取起始位置（搜索文本后面）
            start_index = search_index + len(search_text)

            # 提取指定数量的字符
            extracted_text = ""
            for i in range(char_count):
                if start_index + i < len(full_text):
                    char = full_text[start_index + i]
                    # 跳过换行符，用空格替代
                    if char == '\n':
                        char = ' '
                    extracted_text += char
                else:
                    # 不足的字符用空格补齐
                    extracted_text += ' '

            # 去除末尾的换行符影响，但保留用户需要的空格
            result = extracted_text.rstrip('\n')

            # 如果结果长度不足，用空格补齐
            while len(result) < char_count:
                result += ' '

            # 确保返回的字符数量正确
            result = result[:char_count]

            if not result.strip():  # 如果提取的内容全是空白
                raise ValueError(f"搜索文本 '{search_text}' 后面没有有效内容")

            return result

        except Exception as e:
            raise Exception(f"Word高级搜索失败: {str(e)}")
    
    def read_excel_content(self, file_path, cell_address='A1'):
        """
        读取Excel文件指定单元格的内容
        
        Args:
            file_path: Excel文件路径
            cell_address: 单元格地址（如A1, B2）
        
        Returns:
            str: 单元格内容
        """
        try:
            # 尝试导入openpyxl
            try:
                from openpyxl import load_workbook
            except ImportError:
                raise ImportError("需要安装openpyxl库: pip install openpyxl")
            
            # 验证单元格地址格式
            if not self.is_valid_cell_address(cell_address):
                raise ValueError(f"无效的单元格地址: {cell_address}")
            
            # 读取工作簿
            workbook = load_workbook(file_path, read_only=True)
            
            # 获取活动工作表
            worksheet = workbook.active
            
            # 读取单元格内容
            cell_value = worksheet[cell_address].value
            
            # 关闭工作簿
            workbook.close()
            
            if cell_value is None:
                raise ValueError(f"单元格 {cell_address} 为空")
            
            # 转换为字符串
            content = str(cell_value).strip()
            
            if not content:
                raise ValueError(f"单元格 {cell_address} 内容为空")
            
            return content
        
        except Exception as e:
            raise Exception(f"读取Excel文件失败: {str(e)}")
    
    def is_valid_cell_address(self, cell_address):
        """
        验证Excel单元格地址是否有效
        
        Args:
            cell_address: 单元格地址
        
        Returns:
            bool: 是否有效
        """
        import re
        # 匹配Excel单元格地址格式（如A1, AB123）
        pattern = r'^[A-Z]+[1-9]\d*$'
        return bool(re.match(pattern, cell_address.upper()))
    
    def get_word_paragraph_count(self, file_path):
        """
        获取Word文档的段落数量
        
        Args:
            file_path: Word文件路径
        
        Returns:
            int: 段落数量
        """
        try:
            from docx import Document
            doc = Document(file_path)
            return len(doc.paragraphs)
        except Exception:
            return 0
    
    def get_excel_sheet_info(self, file_path):
        """
        获取Excel文件的工作表信息
        
        Args:
            file_path: Excel文件路径
        
        Returns:
            dict: 包含工作表名称和大小信息
        """
        try:
            from openpyxl import load_workbook
            workbook = load_workbook(file_path, read_only=True)
            
            info = {
                'sheet_names': workbook.sheetnames,
                'active_sheet': workbook.active.title,
                'max_row': workbook.active.max_row,
                'max_column': workbook.active.max_column
            }
            
            workbook.close()
            return info
        except Exception:
            return {}
    
    def preview_content(self, file_path, word_paragraph=1, excel_cell='A1'):
        """
        预览文件内容（用于界面显示）
        
        Args:
            file_path: 文件路径
            word_paragraph: Word段落位置
            excel_cell: Excel单元格位置
        
        Returns:
            dict: 包含内容和额外信息的字典
        """
        result = {
            'content': None,
            'error': None,
            'info': {}
        }
        
        try:
            content = self.read_file_content(file_path, word_paragraph, excel_cell)
            result['content'] = content
            
            # 添加额外信息
            file_path = Path(file_path)
            extension = file_path.suffix.lower()
            
            if extension in self.supported_word_extensions:
                result['info']['paragraph_count'] = self.get_word_paragraph_count(file_path)
            elif extension in self.supported_excel_extensions:
                result['info']['sheet_info'] = self.get_excel_sheet_info(file_path)
        
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def is_supported_file(self, file_path):
        """
        检查文件是否支持内容读取
        
        Args:
            file_path: 文件路径
        
        Returns:
            bool: 是否支持
        """
        extension = Path(file_path).suffix.lower()
        return extension in (self.supported_word_extensions + self.supported_excel_extensions)
    
    def get_supported_extensions(self):
        """
        获取支持的文件扩展名列表
        
        Returns:
            list: 支持的扩展名列表
        """
        return self.supported_word_extensions + self.supported_excel_extensions
