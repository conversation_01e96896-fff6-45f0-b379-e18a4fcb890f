#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重命名引擎模块 - 处理文件重命名的核心逻辑
"""

import os
import shutil
from pathlib import Path
from datetime import datetime, timedelta
import re

class RenameEngine:
    """文件重命名引擎"""
    
    def __init__(self):
        self.backup_info = []  # 用于撤销操作的备份信息
    
    def generate_pattern_names(self, file_count, pattern_type, start_value="1", 
                              prefix="", suffix="", padding=0):
        """
        生成规律命名的名称列表
        
        Args:
            file_count: 文件数量
            pattern_type: 模式类型 ('number', 'letter', 'date')
            start_value: 起始值
            prefix: 前缀
            suffix: 后缀
            padding: 数字补零位数
        
        Returns:
            list: 生成的名称列表
        """
        names = []
        
        if pattern_type == "number":
            start_num = int(start_value) if start_value.isdigit() else 1
            for i in range(file_count):
                if padding > 0:
                    number = str(start_num + i).zfill(padding)
                else:
                    number = str(start_num + i)
                name = f"{prefix}{number}{suffix}"
                names.append(name)
        
        elif pattern_type == "letter":
            start_ord = ord(start_value.upper()) if start_value.isalpha() else ord('A')
            for i in range(file_count):
                if start_ord + i <= ord('Z'):
                    letter = chr(start_ord + i)
                else:
                    # 超过Z后使用AA, AB, AC...
                    extra = (start_ord + i - ord('A')) // 26
                    letter = chr(ord('A') + extra - 1) + chr(ord('A') + (start_ord + i - ord('A')) % 26)
                
                if start_value.islower():
                    letter = letter.lower()
                
                name = f"{prefix}{letter}{suffix}"
                names.append(name)
        
        elif pattern_type == "date":
            # 使用pattern_generator来生成日期序列
            from pattern_generator import PatternGenerator
            generator = PatternGenerator()
            names = generator.generate_date_sequence(
                file_count,
                start_value if start_value else None,
                "%Y%m%d",
                1,  # 每天递增1天
                prefix,
                suffix
            )
        
        return names
    
    def load_custom_names(self, file_path):
        """
        从文件加载自定义名称列表

        Args:
            file_path: 名称文件路径（支持txt、csv、xlsx、xls格式）

        Returns:
            list: 名称列表
        """
        names = []
        file_path = file_path.strip()
        file_extension = Path(file_path).suffix.lower()

        try:
            if file_extension == '.txt':
                # 读取文本文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:  # 跳过空行
                            names.append(line)

            elif file_extension == '.csv':
                # 读取CSV文件
                import csv
                with open(file_path, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    for row in reader:
                        if row:  # 跳过空行
                            names.append(row[0].strip())

            elif file_extension in ['.xlsx', '.xls']:
                # 读取Excel文件
                try:
                    from openpyxl import load_workbook
                except ImportError:
                    raise Exception("需要安装openpyxl库来读取Excel文件: pip install openpyxl")

                workbook = load_workbook(file_path, read_only=True)
                worksheet = workbook.active

                # 读取第一列的所有非空单元格
                for row in worksheet.iter_rows(min_col=1, max_col=1, values_only=True):
                    if row[0] is not None:
                        name = str(row[0]).strip()
                        if name:  # 跳过空值
                            names.append(name)

                workbook.close()

            else:
                raise Exception(f"不支持的文件格式: {file_extension}。支持的格式：.txt, .csv, .xlsx, .xls")

        except Exception as e:
            raise Exception(f"读取名称文件失败: {str(e)}")

        return names
    
    def generate_new_names(self, file_paths, naming_mode, config):
        """
        根据配置生成新的文件名
        
        Args:
            file_paths: 文件路径列表
            naming_mode: 命名模式 ('pattern', 'custom', 'content')
            config: 配置字典
        
        Returns:
            list: 包含(原路径, 新名称, 状态)的元组列表
        """
        results = []
        
        if naming_mode == "pattern":
            # 规律命名
            names = self.generate_pattern_names(
                len(file_paths),
                config.get('pattern_type', 'number'),
                config.get('start_value', '1'),
                config.get('prefix', ''),
                config.get('suffix', ''),
                config.get('padding', 0)
            )
            
            for i, file_path in enumerate(file_paths):
                if i < len(names):
                    # 保留原文件扩展名
                    original_ext = Path(file_path).suffix
                    new_name = names[i] + original_ext
                    results.append((file_path, new_name, "准备就绪"))
                else:
                    results.append((file_path, "", "名称不足"))
        
        elif naming_mode == "custom":
            # 用户自定义命名
            try:
                custom_names = self.load_custom_names(config.get('custom_file', ''))
                
                for i, file_path in enumerate(file_paths):
                    if i < len(custom_names):
                        # 保留原文件扩展名
                        original_ext = Path(file_path).suffix
                        new_name = custom_names[i] + original_ext
                        results.append((file_path, new_name, "准备就绪"))
                    else:
                        results.append((file_path, "", "名称不足"))
            
            except Exception as e:
                for file_path in file_paths:
                    results.append((file_path, "", f"错误: {str(e)}"))
        
        elif naming_mode == "content":
            # 内容读取命名
            from file_reader import FileContentReader
            reader = FileContentReader()
            
            for file_path in file_paths:
                try:
                    content = reader.read_file_content(
                        file_path,
                        config.get('word_paragraph', 1),
                        config.get('excel_cell', 'A1'),
                        config.get('word_search_text'),
                        config.get('word_char_count')
                    )
                    
                    if content:
                        # 清理文件名中的非法字符
                        clean_content = self.clean_filename(content)
                        original_ext = Path(file_path).suffix
                        new_name = clean_content + original_ext
                        results.append((file_path, new_name, "准备就绪"))
                    else:
                        results.append((file_path, "", "无法读取内容"))
                
                except Exception as e:
                    results.append((file_path, "", f"错误: {str(e)}"))
        
        return results
    
    def clean_filename(self, filename):
        """
        清理文件名中的非法字符
        
        Args:
            filename: 原始文件名
        
        Returns:
            str: 清理后的文件名
        """
        # Windows文件名非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        cleaned = re.sub(illegal_chars, '_', filename)
        
        # 移除首尾空格和点
        cleaned = cleaned.strip(' .')
        
        # 限制长度
        if len(cleaned) > 200:
            cleaned = cleaned[:200]
        
        return cleaned if cleaned else "unnamed"
    
    def preview_rename(self, rename_list):
        """
        预览重命名操作
        
        Args:
            rename_list: 重命名列表 [(原路径, 新名称, 状态)]
        
        Returns:
            list: 预览结果列表
        """
        preview_results = []
        
        for original_path, new_name, status in rename_list:
            if status == "准备就绪" and new_name:
                original_file = Path(original_path)
                new_path = original_file.parent / new_name
                
                # 检查新文件名是否已存在
                if new_path.exists() and str(new_path) != original_path:
                    preview_results.append((original_path, new_name, "文件已存在"))
                else:
                    preview_results.append((original_path, new_name, "可以重命名"))
            else:
                preview_results.append((original_path, new_name, status))
        
        return preview_results
    
    def execute_rename(self, rename_list, backup=True):
        """
        执行重命名操作
        
        Args:
            rename_list: 重命名列表 [(原路径, 新名称, 状态)]
            backup: 是否备份操作信息用于撤销
        
        Returns:
            list: 执行结果列表
        """
        results = []
        current_backup = []
        
        for original_path, new_name, status in rename_list:
            if status in ["准备就绪", "可以重命名"] and new_name:
                try:
                    original_file = Path(original_path)
                    new_path = original_file.parent / new_name
                    
                    # 检查目标文件是否已存在
                    if new_path.exists() and str(new_path) != original_path:
                        results.append((original_path, new_name, "文件已存在，跳过"))
                        continue
                    
                    # 执行重命名
                    original_file.rename(new_path)
                    
                    # 记录备份信息
                    if backup:
                        current_backup.append((str(new_path), str(original_file)))
                    
                    results.append((original_path, new_name, "重命名成功"))
                
                except Exception as e:
                    results.append((original_path, new_name, f"重命名失败: {str(e)}"))
            else:
                results.append((original_path, new_name, status))
        
        # 保存备份信息
        if backup and current_backup:
            self.backup_info.append(current_backup)
        
        return results
    
    def undo_last_rename(self):
        """
        撤销最后一次重命名操作
        
        Returns:
            tuple: (是否成功, 消息)
        """
        if not self.backup_info:
            return False, "没有可撤销的操作"
        
        last_backup = self.backup_info.pop()
        success_count = 0
        error_count = 0
        
        for current_path, original_path in reversed(last_backup):
            try:
                Path(current_path).rename(original_path)
                success_count += 1
            except Exception as e:
                error_count += 1
                print(f"撤销失败: {current_path} -> {original_path}, 错误: {e}")
        
        if error_count == 0:
            return True, f"成功撤销 {success_count} 个文件的重命名"
        else:
            return False, f"撤销完成，成功 {success_count} 个，失败 {error_count} 个"
    
    def clear_backup(self):
        """清空备份信息"""
        self.backup_info.clear()
