# 阿里云表格存储部署指南

## 📋 迁移完成状态

### ✅ 已完成的工作：
1. **配置文件更新**：在 `admin_config.py` 中添加了阿里云表格存储配置
2. **云函数代码重写**：将 `cloud_function.py` 从SQLite完全迁移到阿里云表格存储
3. **依赖管理**：创建了 `cloud_function_requirements.txt` 文件

### 🔧 代码修改详情：
- 替换了所有数据库操作为表格存储API调用
- 保持了原有的业务逻辑不变
- 添加了表格存储客户端连接管理
- 更新了错误处理机制

## 🚀 下一步部署操作

### 1. 确认表格存储表结构

请在阿里云表格存储控制台确认三个表的主键设置：

#### `device_records` 表
- **主键**：`device_id` (String)

#### `activation_codes` 表  
- **主键**：`code` (String)

#### `usage_logs` 表
- **主键**：`log_id` (String)

### 2. 部署云函数

#### 2.1 安装依赖
在云函数部署时，需要安装表格存储SDK：
```bash
pip install tablestore>=5.4.0
```

#### 2.2 设置环境变量
在腾讯云函数控制台设置以下环境变量：
```
OTS_ENDPOINT=https://piliang-ots.cn-beijing.ots.aliyuncs.com
OTS_ACCESS_KEY_ID=LTAI5t87menNDTJpcBnLPerh
OTS_ACCESS_KEY_SECRET=******************************
OTS_INSTANCE_NAME=piliang-ots
ADMIN_SECRET_KEY=010bhqlijia1
```

#### 2.3 上传代码
将修改后的 `cloud_function.py` 上传到腾讯云函数

### 3. 测试验证

#### 3.1 测试连接
首先测试表格存储连接是否正常

#### 3.2 测试功能
- 设备状态检查 (`/check`)
- 软件使用记录 (`/use`) 
- 设备激活 (`/activate`)
- 激活码管理 (`/admin/add_codes`)

### 4. 数据迁移（如果需要）

如果之前有SQLite数据需要迁移，需要：
1. 导出现有SQLite数据
2. 转换为表格存储格式
3. 批量导入到阿里云表格存储

## ⚠️ 注意事项

### 安全提醒：
- AccessKey信息已配置在代码中，部署后请确保环境变量正确设置
- 建议定期轮换AccessKey
- 监控表格存储的访问日志

### 成本考虑：
- 表格存储按读写次数和存储量计费
- 建议设置合理的监控和告警

### 性能优化：
- 表格存储支持高并发访问
- 可以根据实际使用情况调整读写容量

## 🔍 故障排查

### 常见问题：
1. **连接失败**：检查AccessKey和Endpoint配置
2. **权限错误**：确认RAM用户有表格存储权限
3. **表不存在**：确认表名和实例名正确

### 调试方法：
- 查看云函数日志
- 检查表格存储访问日志
- 验证网络连通性

## 📊 监控建议

建议监控以下指标：
- 表格存储读写QPS
- 云函数调用次数和耗时
- 错误率和异常日志
- 存储容量使用情况

部署完成后，原有的SQLite临时存储将被完全替换为阿里云表格存储的持久化存储。
