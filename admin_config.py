#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员配置文件 - 包含敏感信息，仅供开发者使用
警告：此文件不要打包到用户端exe中！
"""

# 云函数API配置
CLOUD_FUNCTION_URL = "https://piliangmingming-azgiihrxxg.cn-beijing.fcapp.run"

# 管理员密钥（敏感信息 - 与云函数环境变量保持一致）
# 请将下面的密钥替换为你自己设定的密钥
ADMIN_SECRET_KEY = "010bhqlijia1"

# 管理员工具配置
DEFAULT_CODE_COUNT = 100  # 默认生成激活码数量
MAX_CODE_COUNT = 10000    # 最大生成激活码数量

# 网络重试配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 1  # 初始重试延迟（秒）
BACKOFF_FACTOR = 1.5  # 退避因子
REQUEST_TIMEOUT = 30  # 请求超时时间（秒）

# 激活码格式配置
CODE_PREFIX = "PREMIUM"
CODE_LENGTH = 6  # 随机部分长度
CHECKSUM_LENGTH = 2  # 校验码长度

# 阿里云表格存储配置
OTS_ENDPOINT = "https://piliang-ots.cn-beijing.ots.aliyuncs.com"
OTS_ACCESS_KEY_ID = "LTAI5t87menNDTJpcBnLPerh"
OTS_ACCESS_KEY_SECRET = "******************************"
OTS_INSTANCE_NAME = "piliang-ots"
OTS_REGION = "cn-beijing"

# 表格存储表名配置
TABLE_DEVICE_RECORDS = "device_records"
TABLE_ACTIVATION_CODES = "activation_codes"
TABLE_USAGE_LOGS = "usage_logs"

# 数据库配置（如果使用独立数据库）
DB_HOST = "your-db-host"
DB_USER = "your-db-user"
DB_PASSWORD = "your-db-password"
DB_NAME = "software_license"

# 邮件配置（用于发送激活码）
SMTP_SERVER = "smtp.qq.com"
SMTP_PORT = 587
EMAIL_USER = "<EMAIL>"
EMAIL_PASSWORD = "your-email-password"

# 统计和监控配置
ENABLE_ANALYTICS = True
LOG_LEVEL = "INFO"
