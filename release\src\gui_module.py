#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI模块 - 批量文件重命名工具的图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from pathlib import Path
from rename_engine import RenameEngine
from file_reader import FileContentReader
from pattern_generator import PatternGenerator
from icon_utils import set_window_icon_safe

class BatchRenameGUI:
    """批量重命名GUI主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("批量文件重命名工具 v1.0")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # 设置窗口图标
        set_window_icon_safe(self.root)

        # 设置最小窗口大小
        self.root.minsize(800, 600)
        
        # 存储选中的文件列表
        self.selected_files = []

        # 存储选择过的文件夹路径（用于刷新）
        self.selected_folders = []

        # 命名配置
        self.naming_config = {}

        # 初始化核心组件
        self.rename_engine = RenameEngine()
        self.file_reader = FileContentReader()
        self.pattern_generator = PatternGenerator()

        # 重命名结果
        self.rename_results = []
        
        # 创建界面
        self.create_widgets()
        
        # 绑定事件
        self.bind_events()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 创建各个区域
        self.create_header(main_frame)
        self.create_file_area(main_frame)
        self.create_naming_area(main_frame)
        self.create_config_area(main_frame)
        self.create_action_area(main_frame)
    
    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        title_label = ttk.Label(header_frame, text="批量文件重命名工具", 
                               font=("Microsoft YaHei", 18, "bold"))
        title_label.pack()
        
        subtitle_label = ttk.Label(header_frame, text="支持用户自定义、内容读取、规律命名三种方式", 
                                  font=("Microsoft YaHei", 10))
        subtitle_label.pack(pady=(5, 0))
    
    def create_file_area(self, parent):
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(parent, text="文件选择", padding="10")
        file_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        file_frame.columnconfigure(0, weight=1)
        file_frame.rowconfigure(1, weight=1)
        
        # 按钮区域
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(button_frame, text="选择文件",
                  command=self.select_files).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="选择文件夹",
                  command=self.select_folder).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空列表",
                  command=self.clear_files).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="刷新列表",
                  command=self.refresh_files).pack(side=tk.LEFT, padx=(0, 10))
        
        # 文件计数标签
        self.file_count_label = ttk.Label(button_frame, text="已选择 0 个文件")
        self.file_count_label.pack(side=tk.RIGHT)
        
        # 文件列表
        self.create_file_list(file_frame)
    
    def create_file_list(self, parent):
        """创建文件列表"""
        list_frame = ttk.Frame(parent)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ("path", "original", "new", "status")
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=12)
        
        # 设置列标题
        self.file_tree.heading("path", text="路径")
        self.file_tree.heading("original", text="原文件名")
        self.file_tree.heading("new", text="新文件名")
        self.file_tree.heading("status", text="状态")
        
        # 设置列宽
        self.file_tree.column("path", width=200)
        self.file_tree.column("original", width=200)
        self.file_tree.column("new", width=200)
        self.file_tree.column("status", width=100)
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.file_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
    
    def create_naming_area(self, parent):
        """创建命名方式选择区域"""
        naming_frame = ttk.LabelFrame(parent, text="命名方式", padding="10")
        naming_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        self.naming_mode = tk.StringVar(value="pattern")
        
        mode_frame = ttk.Frame(naming_frame)
        mode_frame.pack(fill=tk.X)
        
        ttk.Radiobutton(mode_frame, text="规律命名", variable=self.naming_mode, 
                       value="pattern", command=self.on_naming_mode_change).pack(side=tk.LEFT, padx=(0, 30))
        ttk.Radiobutton(mode_frame, text="用户自定义", variable=self.naming_mode, 
                       value="custom", command=self.on_naming_mode_change).pack(side=tk.LEFT, padx=(0, 30))
        ttk.Radiobutton(mode_frame, text="内容读取", variable=self.naming_mode, 
                       value="content", command=self.on_naming_mode_change).pack(side=tk.LEFT)
    
    def create_config_area(self, parent):
        """创建配置区域"""
        self.config_frame = ttk.LabelFrame(parent, text="配置选项", padding="10")
        self.config_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # 初始显示规律命名配置
        self.show_pattern_config()
    
    def create_action_area(self, parent):
        """创建操作按钮区域"""
        action_frame = ttk.Frame(parent)
        action_frame.grid(row=4, column=0, pady=(10, 0))

        # 主要功能按钮（左侧）
        main_buttons_frame = ttk.Frame(action_frame)
        main_buttons_frame.pack(side=tk.LEFT)

        ttk.Button(main_buttons_frame, text="预览重命名",
                  command=self.preview_rename).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(main_buttons_frame, text="执行重命名",
                  command=self.execute_rename).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(main_buttons_frame, text="撤销操作",
                  command=self.undo_rename).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(main_buttons_frame, text="退出程序",
                  command=self.on_closing).pack(side=tk.LEFT, padx=(0, 15))

        # 激活按钮（右侧）
        activation_frame = ttk.Frame(action_frame)
        activation_frame.pack(side=tk.RIGHT)

        ttk.Button(activation_frame, text="软件激活",
                  command=self.show_activation_dialog,
                  style="Accent.TButton").pack(side=tk.RIGHT)
    
    def show_pattern_config(self):
        """显示规律命名配置"""
        self.clear_config_area()
        
        # 序列类型选择
        type_frame = ttk.Frame(self.config_frame)
        type_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(type_frame, text="序列类型:").pack(side=tk.LEFT)
        self.pattern_type = tk.StringVar(value="number")
        ttk.Radiobutton(type_frame, text="数字", variable=self.pattern_type, 
                       value="number").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Radiobutton(type_frame, text="字母", variable=self.pattern_type, 
                       value="letter").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Radiobutton(type_frame, text="日期", variable=self.pattern_type, 
                       value="date").pack(side=tk.LEFT, padx=(10, 0))
        
        # 配置选项
        option_frame = ttk.Frame(self.config_frame)
        option_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(option_frame, text="起始值:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.start_value = tk.StringVar(value="1")
        self.start_entry = ttk.Entry(option_frame, textvariable=self.start_value, width=15)
        self.start_entry.grid(row=0, column=1, padx=(0, 10))

        # 添加格式提醒标签（初始为空，根据模式动态更新）
        self.format_hint_label = ttk.Label(option_frame, text="", foreground="gray", font=("Microsoft YaHei", 8))
        self.format_hint_label.grid(row=0, column=2, sticky=tk.W, padx=(0, 20))
        
        ttk.Label(option_frame, text="前缀:").grid(row=0, column=3, sticky=tk.W, padx=(0, 5))
        self.prefix = tk.StringVar()
        ttk.Entry(option_frame, textvariable=self.prefix, width=15).grid(row=0, column=4, padx=(0, 20))

        ttk.Label(option_frame, text="后缀:").grid(row=0, column=5, sticky=tk.W, padx=(0, 5))
        self.suffix = tk.StringVar()
        ttk.Entry(option_frame, textvariable=self.suffix, width=15).grid(row=0, column=6)

        # 绑定模式变化事件来更新格式提醒
        self.pattern_type.trace('w', self.update_format_hint)

        # 初始化格式提醒
        self.update_format_hint()
    
    def show_custom_config(self):
        """显示用户自定义配置"""
        self.clear_config_area()

        # 添加文件类型提示
        hint_frame = ttk.Frame(self.config_frame)
        hint_frame.pack(fill=tk.X, pady=(0, 10))

        hint_label = ttk.Label(hint_frame,
                              text="支持的文件格式：.txt（每行一个名称）、.csv（第一列）、.xlsx/.xls（第一列）",
                              foreground="gray", font=("Microsoft YaHei", 9))
        hint_label.pack(anchor=tk.W)

        # 文件选择区域
        file_frame = ttk.Frame(self.config_frame)
        file_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(file_frame, text="名称文件:").pack(side=tk.LEFT)
        self.custom_file_path = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.custom_file_path, width=50).pack(side=tk.LEFT, padx=(10, 10))
        ttk.Button(file_frame, text="浏览", command=self.select_custom_file).pack(side=tk.LEFT)
    
    def show_content_config(self):
        """显示内容读取配置"""
        self.clear_config_area()

        ttk.Label(self.config_frame, text="此模式仅适用于Word和Excel文件").pack(pady=(0, 10))

        # Word配置
        word_frame = ttk.LabelFrame(self.config_frame, text="Word文档配置", padding="5")
        word_frame.pack(fill=tk.X, pady=(0, 10))

        # Word模式选择
        self.word_mode = tk.StringVar(value="paragraph")

        mode_frame = ttk.Frame(word_frame)
        mode_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Radiobutton(mode_frame, text="基础模式", variable=self.word_mode,
                       value="paragraph", command=self.update_word_config).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(mode_frame, text="高级模式", variable=self.word_mode,
                       value="advanced", command=self.update_word_config).pack(side=tk.LEFT)

        # Word配置区域
        self.word_config_frame = ttk.Frame(word_frame)
        self.word_config_frame.pack(fill=tk.X)

        # 初始化Word配置
        self.update_word_config()

        # Excel配置
        excel_frame = ttk.LabelFrame(self.config_frame, text="Excel文件配置", padding="5")
        excel_frame.pack(fill=tk.X)

        ttk.Label(excel_frame, text="单元格位置 (如A1, B2):").pack(side=tk.LEFT)
        self.excel_cell = tk.StringVar(value="A1")
        ttk.Entry(excel_frame, textvariable=self.excel_cell, width=10).pack(side=tk.LEFT, padx=(10, 0))
    
    def update_word_config(self):
        """更新Word配置界面"""
        # 清空Word配置区域
        for widget in self.word_config_frame.winfo_children():
            widget.destroy()

        mode = self.word_mode.get()

        if mode == "paragraph":
            # 基础模式：段落读取
            basic_frame = ttk.Frame(self.word_config_frame)
            basic_frame.pack(fill=tk.X)

            ttk.Label(basic_frame, text="段落编号 (从1开始):").pack(side=tk.LEFT)
            self.word_paragraph = tk.StringVar(value="1")
            ttk.Entry(basic_frame, textvariable=self.word_paragraph, width=10).pack(side=tk.LEFT, padx=(10, 20))

            # 添加说明
            ttk.Label(basic_frame, text="(输入的数字代表段落编号)",
                     foreground="gray", font=("Microsoft YaHei", 8)).pack(side=tk.LEFT)

        elif mode == "advanced":
            # 高级模式：搜索文本
            advanced_frame = ttk.Frame(self.word_config_frame)
            advanced_frame.pack(fill=tk.X)

            # 第一行：搜索内容
            search_frame = ttk.Frame(advanced_frame)
            search_frame.pack(fill=tk.X, pady=(0, 5))

            ttk.Label(search_frame, text="搜索内容:").pack(side=tk.LEFT)
            self.word_search_text = tk.StringVar()
            ttk.Entry(search_frame, textvariable=self.word_search_text, width=20).pack(side=tk.LEFT, padx=(10, 20))

            ttk.Label(search_frame, text="字符数:").pack(side=tk.LEFT)
            self.word_char_count = tk.StringVar(value="10")
            ttk.Entry(search_frame, textvariable=self.word_char_count, width=8).pack(side=tk.LEFT, padx=(10, 0))

            # 第二行：说明
            hint_frame = ttk.Frame(advanced_frame)
            hint_frame.pack(fill=tk.X)

            hint_text = "提取搜索内容后面指定数量的字符（空格也算字符，不足时用空格补齐）"
            ttk.Label(hint_frame, text=hint_text,
                     foreground="gray", font=("Microsoft YaHei", 8)).pack(anchor=tk.W)

    def clear_config_area(self):
        """清空配置区域"""
        for widget in self.config_frame.winfo_children():
            widget.destroy()
    
    def bind_events(self):
        """绑定事件"""
        pass
    
    def on_naming_mode_change(self):
        """命名方式改变事件"""
        mode = self.naming_mode.get()
        if mode == "pattern":
            self.show_pattern_config()
        elif mode == "custom":
            self.show_custom_config()
        elif mode == "content":
            self.show_content_config()

    def update_format_hint(self, *args):
        """更新格式提醒"""
        if hasattr(self, 'format_hint_label') and hasattr(self, 'pattern_type'):
            pattern_type = self.pattern_type.get()
            if pattern_type == "number":
                self.format_hint_label.config(text="(数字，如: 1)")
                self.start_value.set("1")
            elif pattern_type == "letter":
                self.format_hint_label.config(text="(字母，如: A)")
                self.start_value.set("A")
            elif pattern_type == "date":
                from datetime import datetime
                today = datetime.now().strftime("%Y-%m-%d")
                self.format_hint_label.config(text="(日期格式: YYYY-MM-DD)")
                self.start_value.set(today)
            else:
                self.format_hint_label.config(text="")
    
    def select_files(self):
        """选择文件"""
        files = filedialog.askopenfilenames(
            title="选择要重命名的文件",
            filetypes=[("所有文件", "*.*")]
        )
        if files:
            for file_path in files:
                if file_path not in self.selected_files:
                    self.selected_files.append(file_path)
            self.update_file_list()
    
    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含文件的文件夹")
        if folder:
            # 记录选择的文件夹路径
            if folder not in self.selected_folders:
                self.selected_folders.append(folder)

            # 添加文件夹内的所有文件
            for file_path in Path(folder).iterdir():
                if file_path.is_file():
                    file_str = str(file_path)
                    if file_str not in self.selected_files:
                        self.selected_files.append(file_str)
            self.update_file_list()
    
    def clear_files(self):
        """清空文件列表"""
        self.selected_files.clear()
        self.selected_folders.clear()  # 同时清空文件夹记录
        self.update_file_list()

    def refresh_files(self):
        """刷新文件列表 - 重新读取文件夹内容"""
        if not self.selected_folders:
            messagebox.showwarning("提示", "没有选择过文件夹，无法刷新！\n请先使用'选择文件夹'功能。")
            return

        # 清空当前文件列表
        old_count = len(self.selected_files)
        self.selected_files.clear()

        # 重新扫描所有选择过的文件夹
        total_files = 0
        for folder in self.selected_folders:
            if os.path.exists(folder):
                folder_path = Path(folder)
                for file_path in folder_path.iterdir():
                    if file_path.is_file():
                        file_str = str(file_path)
                        if file_str not in self.selected_files:
                            self.selected_files.append(file_str)
                            total_files += 1

        # 更新显示
        self.update_file_list()

        # 显示刷新结果
        messagebox.showinfo("刷新完成",
                          f"重新扫描了 {len(self.selected_folders)} 个文件夹\n"
                          f"原有文件：{old_count} 个\n"
                          f"当前文件：{len(self.selected_files)} 个")
    
    def select_custom_file(self):
        """选择自定义名称文件"""
        file_path = filedialog.askopenfilename(
            title="选择包含名称的文件",
            filetypes=[
                ("支持的文件", "*.txt;*.csv;*.xlsx;*.xls"),
                ("文本文件", "*.txt"),
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx;*.xls"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.custom_file_path.set(file_path)
    
    def update_file_list(self):
        """更新文件列表显示"""
        # 清空现有项目
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)
        
        # 添加文件
        for file_path in self.selected_files:
            path_obj = Path(file_path)
            directory = str(path_obj.parent)
            filename = path_obj.name
            self.file_tree.insert("", tk.END, values=(directory, filename, "待配置", "待处理"))
        
        # 更新文件计数
        count = len(self.selected_files)
        self.file_count_label.config(text=f"已选择 {count} 个文件")
    
    def preview_rename(self):
        """预览重命名"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要重命名的文件！")
            return

        try:
            # 获取当前配置
            config = self.get_current_config()
            naming_mode = self.naming_mode.get()

            # 生成新名称
            rename_list = self.rename_engine.generate_new_names(
                self.selected_files, naming_mode, config
            )

            # 预览结果
            preview_results = self.rename_engine.preview_rename(rename_list)

            # 更新文件列表显示
            self.update_file_list_with_results(preview_results)

            messagebox.showinfo("预览完成", f"已生成 {len(preview_results)} 个文件的重命名预览")

        except Exception as e:
            messagebox.showerror("预览失败", f"预览重命名时发生错误：\n{str(e)}")

    def execute_rename(self):
        """执行重命名"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要重命名的文件！")
            return

        # 确认执行
        if not messagebox.askyesno("确认重命名", "确定要执行重命名操作吗？\n此操作不可逆！"):
            return

        try:
            # 获取当前配置
            config = self.get_current_config()
            naming_mode = self.naming_mode.get()

            # 生成新名称
            rename_list = self.rename_engine.generate_new_names(
                self.selected_files, naming_mode, config
            )

            # 执行重命名
            results = self.rename_engine.execute_rename(rename_list, backup=True)

            # 保存结果
            self.rename_results = results

            # 更新文件列表显示
            self.update_file_list_with_results(results)

            # 统计结果
            success_count = sum(1 for _, _, status in results if "成功" in status)
            total_count = len(results)

            messagebox.showinfo("重命名完成",
                              f"重命名操作完成！\n成功：{success_count}/{total_count}")

        except Exception as e:
            messagebox.showerror("重命名失败", f"执行重命名时发生错误：\n{str(e)}")

    def undo_rename(self):
        """撤销重命名"""
        try:
            success, message = self.rename_engine.undo_last_rename()
            if success:
                messagebox.showinfo("撤销成功", message)
                # 刷新文件列表
                self.update_file_list()
            else:
                messagebox.showwarning("撤销失败", message)
        except Exception as e:
            messagebox.showerror("撤销错误", f"撤销操作时发生错误：\n{str(e)}")
    
    def get_current_config(self):
        """获取当前配置"""
        config = {}
        naming_mode = self.naming_mode.get()

        if naming_mode == "pattern":
            config['pattern_type'] = getattr(self, 'pattern_type', tk.StringVar(value="number")).get()
            config['start_value'] = getattr(self, 'start_value', tk.StringVar(value="1")).get()
            config['prefix'] = getattr(self, 'prefix', tk.StringVar()).get()
            config['suffix'] = getattr(self, 'suffix', tk.StringVar()).get()

        elif naming_mode == "custom":
            config['custom_file'] = getattr(self, 'custom_file_path', tk.StringVar()).get()

        elif naming_mode == "content":
            # Word配置
            word_mode = getattr(self, 'word_mode', tk.StringVar(value="paragraph")).get()
            if word_mode == "paragraph":
                config['word_paragraph'] = int(getattr(self, 'word_paragraph', tk.StringVar(value="1")).get() or 1)
            elif word_mode == "advanced":
                config['word_search_text'] = getattr(self, 'word_search_text', tk.StringVar()).get()
                try:
                    config['word_char_count'] = int(getattr(self, 'word_char_count', tk.StringVar(value="10")).get() or 10)
                except ValueError:
                    config['word_char_count'] = 10

            # Excel配置
            config['excel_cell'] = getattr(self, 'excel_cell', tk.StringVar(value="A1")).get()

        return config

    def update_file_list_with_results(self, results):
        """使用结果更新文件列表显示"""
        # 清空现有项目
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # 添加结果
        for original_path, new_name, status in results:
            path_obj = Path(original_path)
            directory = str(path_obj.parent)
            original_filename = path_obj.name

            # 根据状态设置颜色标记
            if "成功" in status:
                tags = ("success",)
            elif "错误" in status or "失败" in status:
                tags = ("error",)
            elif "警告" in status or "已存在" in status:
                tags = ("warning",)
            else:
                tags = ("normal",)

            self.file_tree.insert("", tk.END,
                                values=(directory, original_filename, new_name or "未设置", status),
                                tags=tags)

        # 配置标签颜色
        self.file_tree.tag_configure("success", background="#d4edda")
        self.file_tree.tag_configure("error", background="#f8d7da")
        self.file_tree.tag_configure("warning", background="#fff3cd")
        self.file_tree.tag_configure("normal", background="white")

    def show_activation_dialog(self):
        """显示激活对话框"""
        # 先计算屏幕中心位置
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = 520
        window_height = 550
        x = (screen_width // 2) - (window_width // 2)
        y = (screen_height // 2) - (window_height // 2)

        # 直接在中心位置创建激活窗口
        activation_window = tk.Toplevel(self.root)
        activation_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        activation_window.title("软件激活")
        activation_window.resizable(False, False)
        activation_window.transient(self.root)
        activation_window.grab_set()

        # 设置窗口图标
        set_window_icon_safe(activation_window)

        # 创建激活窗口内容
        self.create_activation_content(activation_window)

    def create_activation_content(self, window):
        """创建激活窗口内容"""
        # 主框架
        main_frame = ttk.Frame(window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="软件激活",
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # 说明文字
        info_label = ttk.Label(main_frame,
                              text="😊帅哥美女，一包白塔，永久激活😊，请选择以下方式获取激活码：",
                              font=("Microsoft YaHei", 10))
        info_label.pack(pady=(0, 20))

        # 链接按钮区域
        links_frame = ttk.LabelFrame(main_frame, text="获取激活码", padding="15")
        links_frame.pack(fill=tk.X, pady=(0, 20))

        # 支付页面链接
        payment_btn = ttk.Button(links_frame, text="💳 在线支付获取激活码",
                                command=self.open_payment_page)
        payment_btn.pack(fill=tk.X, pady=(0, 10))

        # QQ群链接
        qq_btn = ttk.Button(links_frame, text="👥 加入QQ群咨询",
                           command=self.open_qq_group)
        qq_btn.pack(fill=tk.X, pady=(0, 10))

        # 邮箱联系
        email_btn = ttk.Button(links_frame, text="📧 邮箱联系购买",
                              command=self.open_email_contact)
        email_btn.pack(fill=tk.X, pady=(0, 10))

        # 官网链接
        website_btn = ttk.Button(links_frame, text="🌐 访问官方网站",
                                command=self.open_official_website)
        website_btn.pack(fill=tk.X)

        # 激活码输入区域
        input_frame = ttk.LabelFrame(main_frame, text="已有激活码", padding="15")
        input_frame.pack(fill=tk.X, pady=(0, 20))

        # 激活码输入
        ttk.Label(input_frame, text="请输入激活码：").pack(anchor=tk.W)

        code_entry_frame = ttk.Frame(input_frame)
        code_entry_frame.pack(fill=tk.X, pady=(5, 10))

        self.activation_code_entry = ttk.Entry(code_entry_frame, font=("Consolas", 11))
        self.activation_code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        activate_btn = ttk.Button(code_entry_frame, text="激活",
                                 command=lambda: self.activate_software(window))
        activate_btn.pack(side=tk.RIGHT)

        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X)

        ttk.Button(bottom_frame, text="关闭",
                  command=window.destroy).pack(side=tk.RIGHT)

    def open_payment_page(self):
        """打开支付页面"""
        import webbrowser
        try:
            from config import PAYMENT_PAGE_URL
            payment_url = PAYMENT_PAGE_URL
        except ImportError:
            payment_url = "https://username.gitee.io/yourapp/payment.html"

        webbrowser.open(payment_url)
        messagebox.showinfo("提示", "支付页面已在浏览器中打开")

    def open_qq_group(self):
        """打开QQ群"""
        import webbrowser
        try:
            from config import QQ_GROUP_URL
            qq_group_url = QQ_GROUP_URL
        except ImportError:
            qq_group_url = "https://qm.qq.com/cgi-bin/qm/qr?k=your_qq_group_key"

        webbrowser.open(qq_group_url)
        messagebox.showinfo("提示", "QQ群页面已在浏览器中打开")

    def open_email_contact(self):
        """打开邮箱联系"""
        import webbrowser
        try:
            from config import EMAIL_CONTACT
            email_url = f"mailto:{EMAIL_CONTACT}?subject=软件激活咨询&body=您好，我想购买软件激活码。"
        except ImportError:
            email_url = "mailto:<EMAIL>?subject=软件激活咨询&body=您好，我想购买软件激活码。"

        webbrowser.open(email_url)
        messagebox.showinfo("提示", "邮箱客户端已打开")

    def open_official_website(self):
        """打开官方网站"""
        import webbrowser
        try:
            from config import OFFICIAL_WEBSITE
            website_url = OFFICIAL_WEBSITE
        except ImportError:
            website_url = "https://yourapp.com"

        webbrowser.open(website_url)
        messagebox.showinfo("提示", "官方网站已在浏览器中打开")

    def activate_software(self, window):
        """激活软件"""
        activation_code = self.activation_code_entry.get().strip()

        if not activation_code:
            messagebox.showwarning("警告", "请输入激活码")
            return

        try:
            from license_manager import LicenseManager

            license_manager = LicenseManager()
            success, result = license_manager.activate_software(activation_code)

            if success and result.get("success"):
                messagebox.showinfo("激活成功", "软件已成功激活！")
                window.destroy()
            else:
                error_msg = result.get("message", "激活失败")
                messagebox.showerror("激活失败", error_msg)

        except ImportError:
            messagebox.showerror("错误", "许可证模块未找到")
        except Exception as e:
            messagebox.showerror("错误", f"激活过程中发生错误：{str(e)}")

    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            self.root.destroy()

    def run(self):
        """运行GUI"""
        self.root.mainloop()
