#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量文件重命名工具
主启动文件

作者：AI Assistant
版本：1.0
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径，确保能导入其他模块
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from gui_module import BatchRenameGUI
    from rename_engine import RenameEngine
    from file_reader import FileContentReader
    from pattern_generator import PatternGenerator
    from icon_utils import set_window_icon_safe
except ImportError as e:
    # 如果模块还未创建，先创建一个简单的GUI
    print(f"模块导入错误: {e}")
    print("将创建基础GUI...")

class SimpleBatchRenameGUI:
    """简单的批量重命名GUI类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("批量文件重命名工具 v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置窗口图标
        try:
            from icon_utils import set_window_icon_safe
            set_window_icon_safe(self.root)
        except:
            pass  # 忽略图标设置错误
        
        # 存储选中的文件列表
        self.selected_files = []
        
        # 创建界面
        self.create_widgets()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="批量文件重命名工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        file_frame.columnconfigure(0, weight=1)
        file_frame.rowconfigure(1, weight=1)
        
        # 按钮区域
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(button_frame, text="选择文件", command=self.select_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="选择文件夹", command=self.select_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空列表", command=self.clear_files).pack(side=tk.LEFT, padx=(0, 5))
        
        # 文件列表
        self.create_file_list(file_frame)
        
        # 命名方式选择区域
        naming_frame = ttk.LabelFrame(main_frame, text="命名方式", padding="10")
        naming_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.naming_mode = tk.StringVar(value="pattern")
        ttk.Radiobutton(naming_frame, text="规律命名", variable=self.naming_mode, 
                       value="pattern").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(naming_frame, text="用户自定义", variable=self.naming_mode, 
                       value="custom").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(naming_frame, text="内容读取", variable=self.naming_mode, 
                       value="content").pack(side=tk.LEFT)
        
        # 配置区域（暂时简化）
        config_frame = ttk.LabelFrame(main_frame, text="配置选项", padding="10")
        config_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(config_frame, text="功能开发中...").pack()
        
        # 操作按钮区域
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=4, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(action_frame, text="预览", command=self.preview_rename).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="执行重命名", command=self.execute_rename).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="退出", command=self.on_closing).pack(side=tk.LEFT)
    
    def create_file_list(self, parent):
        """创建文件列表"""
        # 创建Treeview
        columns = ("original", "new", "status")
        self.file_tree = ttk.Treeview(parent, columns=columns, show="headings", height=10)
        
        # 设置列标题
        self.file_tree.heading("original", text="原文件名")
        self.file_tree.heading("new", text="新文件名")
        self.file_tree.heading("status", text="状态")
        
        # 设置列宽
        self.file_tree.column("original", width=250)
        self.file_tree.column("new", width=250)
        self.file_tree.column("status", width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.file_tree.yview)
        self.file_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.file_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
    
    def select_files(self):
        """选择文件"""
        files = filedialog.askopenfilenames(
            title="选择要重命名的文件",
            filetypes=[("所有文件", "*.*")]
        )
        if files:
            for file_path in files:
                if file_path not in self.selected_files:
                    self.selected_files.append(file_path)
            self.update_file_list()
    
    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含文件的文件夹")
        if folder:
            for file_path in Path(folder).iterdir():
                if file_path.is_file():
                    file_str = str(file_path)
                    if file_str not in self.selected_files:
                        self.selected_files.append(file_str)
            self.update_file_list()
    
    def clear_files(self):
        """清空文件列表"""
        self.selected_files.clear()
        self.update_file_list()
    
    def update_file_list(self):
        """更新文件列表显示"""
        # 清空现有项目
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)
        
        # 添加文件
        for file_path in self.selected_files:
            filename = os.path.basename(file_path)
            self.file_tree.insert("", tk.END, values=(filename, "待配置", "待处理"))
    
    def preview_rename(self):
        """预览重命名"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要重命名的文件！")
            return
        messagebox.showinfo("提示", "预览功能开发中...")
    
    def execute_rename(self):
        """执行重命名"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要重命名的文件！")
            return
        messagebox.showinfo("提示", "重命名功能开发中...")
    
    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def check_license_and_start():
    """检查许可证并启动程序"""
    try:
        from license_manager import LicenseManager

        # 初始化许可证管理器
        license_manager = LicenseManager()

        print("正在验证软件许可证...")

        # 检查许可证状态
        can_use, status = license_manager.can_use_software()

        print(f"许可证状态: {license_manager.get_status_message(status)}")

        if status.get("is_activated"):
            # 已激活，直接启动
            print("软件已激活，正常启动...")
            start_main_application()

        elif can_use and status.get("remaining_trials", 0) > 0:
            # 有试用次数，记录使用并启动
            print("使用试用版本...")

            # 记录本次使用
            success, usage_result = license_manager.record_software_usage()
            if success and usage_result.get("success"):
                remaining = usage_result.get("remaining_trials", 0)
                print(f"试用次数记录成功，剩余 {remaining} 次")
                start_main_application()
            else:
                print("记录使用失败，可能是网络问题")
                # 即使记录失败，也允许使用（离线模式）
                start_main_application()

        else:
            # 试用次数用完或未激活
            print("需要激活软件...")
            show_activation_dialog(license_manager)

    except ImportError:
        print("许可证模块未找到，启动基础版本...")
        start_main_application()
    except Exception as e:
        print(f"许可证验证失败: {e}")
        print("启动基础版本...")
        start_main_application()

def show_activation_choice_dialog(parent):
    """显示激活选择对话框"""
    # 先计算屏幕中心位置
    screen_width = parent.winfo_screenwidth()
    screen_height = parent.winfo_screenheight()
    window_width = 450
    window_height = 300
    x = (screen_width // 2) - (window_width // 2)
    y = (screen_height // 2) - (window_height // 2)

    # 直接在中心位置创建自定义对话框
    dialog = tk.Toplevel(parent)
    dialog.geometry(f"{window_width}x{window_height}+{x}+{y}")
    dialog.title("软件激活")
    dialog.resizable(False, False)
    dialog.transient(parent)
    dialog.grab_set()

    # 设置窗口图标
    set_window_icon_safe(dialog)

    # 存储用户选择
    choice = [None]

    # 主框架
    main_frame = ttk.Frame(dialog, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)

    # 标题
    title_label = ttk.Label(main_frame, text="试用次数已用完！",
                           font=("Microsoft YaHei", 14, "bold"),
                           foreground="red")
    title_label.pack(pady=(0, 15))

    # 说明文字
    info_label = ttk.Label(main_frame,
                          text="😊帅哥美女，一包白塔，永久激活😊，请选择以下操作：",
                          font=("Microsoft YaHei", 10))
    info_label.pack(pady=(0, 20))

    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)

    # 直接输入激活码按钮
    activate_btn = ttk.Button(button_frame, text="🔑 输入激活码",
                             command=lambda: [choice.__setitem__(0, 'activate'), dialog.destroy()])
    activate_btn.pack(fill=tk.X, pady=(0, 10))

    # 获取激活码按钮
    get_code_btn = ttk.Button(button_frame, text="🛒 获取激活码",
                             command=lambda: [choice.__setitem__(0, 'get_code'), dialog.destroy()])
    get_code_btn.pack(fill=tk.X, pady=(0, 10))

    # 退出程序按钮
    exit_btn = ttk.Button(button_frame, text="❌ 退出程序",
                         command=lambda: [choice.__setitem__(0, 'exit'), dialog.destroy()])
    exit_btn.pack(fill=tk.X)

    # 等待用户选择
    dialog.wait_window()
    return choice[0]

def activate_with_code(parent, license_manager):
    """直接输入激活码激活"""
    while True:
        activation_code = simpledialog.askstring(
            "输入激活码",
            "请输入购买的激活码：",
            parent=parent
        )

        if not activation_code:
            # 用户取消输入
            break

        print(f"正在验证激活码...")
        success, result = license_manager.activate_software(activation_code.strip())

        if success and result.get("success"):
            messagebox.showinfo("激活成功", "软件已成功激活！即将启动...")
            parent.destroy()
            start_main_application()
            return
        else:
            error_msg = result.get("message", "激活失败")
            retry = messagebox.askretrycancel("激活失败", f"{error_msg}\n\n是否重试？")
            if not retry:
                break

def show_full_activation_dialog(parent, license_manager):
    """显示完整的激活对话框（获取+输入）"""
    # 先计算屏幕中心位置
    screen_width = parent.winfo_screenwidth()
    screen_height = parent.winfo_screenheight()
    window_width = 520
    window_height = 550
    x = (screen_width // 2) - (window_width // 2)
    y = (screen_height // 2) - (window_height // 2)

    # 直接在中心位置创建完整激活窗口
    activation_window = tk.Toplevel(parent)
    activation_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
    activation_window.title("软件激活")
    activation_window.resizable(False, False)
    activation_window.transient(parent)
    activation_window.grab_set()

    # 设置窗口图标
    set_window_icon_safe(activation_window)

    # 创建激活窗口内容（复用gui_module的逻辑）
    create_full_activation_content(activation_window, parent, license_manager)

    # 等待窗口操作完成（阻塞式）
    activation_window.wait_window()

def create_full_activation_content(window, parent, license_manager):
    """创建完整激活窗口内容"""
    # 主框架
    main_frame = ttk.Frame(window, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)

    # 标题
    title_label = ttk.Label(main_frame, text="软件激活",
                           font=("Microsoft YaHei", 16, "bold"))
    title_label.pack(pady=(0, 20))

    # 说明文字
    info_label = ttk.Label(main_frame,
                          text="😊帅哥美女，一包白塔，永久激活😊请选择以下方式获取激活码：",
                          font=("Microsoft YaHei", 10))
    info_label.pack(pady=(0, 20))

    # 链接按钮区域
    links_frame = ttk.LabelFrame(main_frame, text="获取激活码", padding="15")
    links_frame.pack(fill=tk.X, pady=(0, 20))

    # 支付页面链接
    payment_btn = ttk.Button(links_frame, text="💳 在线支付获取激活码",
                            command=open_payment_page)
    payment_btn.pack(fill=tk.X, pady=(0, 10))

    # QQ群链接
    qq_btn = ttk.Button(links_frame, text="👥 加入QQ群咨询",
                       command=open_qq_group)
    qq_btn.pack(fill=tk.X, pady=(0, 10))

    # 邮箱联系
    email_btn = ttk.Button(links_frame, text="📧 邮箱联系购买",
                          command=open_email_contact)
    email_btn.pack(fill=tk.X, pady=(0, 10))

    # 官网链接
    website_btn = ttk.Button(links_frame, text="🌐 访问官方网站",
                            command=open_official_website)
    website_btn.pack(fill=tk.X)

    # 激活码输入区域
    input_frame = ttk.LabelFrame(main_frame, text="已有激活码", padding="15")
    input_frame.pack(fill=tk.X, pady=(0, 20))

    # 激活码输入
    ttk.Label(input_frame, text="请输入激活码：").pack(anchor=tk.W)

    code_entry_frame = ttk.Frame(input_frame)
    code_entry_frame.pack(fill=tk.X, pady=(5, 10))

    activation_code_entry = ttk.Entry(code_entry_frame, font=("Consolas", 11))
    activation_code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

    def activate_software_action():
        """激活软件操作"""
        activation_code = activation_code_entry.get().strip()

        if not activation_code:
            messagebox.showwarning("警告", "请输入激活码")
            return

        print(f"正在验证激活码...")
        success, result = license_manager.activate_software(activation_code)

        if success and result.get("success"):
            messagebox.showinfo("激活成功", "软件已成功激活！即将启动...")
            window.destroy()
            parent.destroy()
            start_main_application()
        else:
            error_msg = result.get("message", "激活失败")
            messagebox.showerror("激活失败", error_msg)

    activate_btn = ttk.Button(code_entry_frame, text="激活",
                             command=activate_software_action)
    activate_btn.pack(side=tk.RIGHT)

    # 底部按钮
    bottom_frame = ttk.Frame(main_frame)
    bottom_frame.pack(fill=tk.X)

    ttk.Button(bottom_frame, text="关闭",
              command=window.destroy).pack(side=tk.RIGHT)

# 辅助函数（复用gui_module的逻辑）
def open_payment_page():
    """打开支付页面"""
    import webbrowser
    try:
        from config import PAYMENT_PAGE_URL
        payment_url = PAYMENT_PAGE_URL
    except ImportError:
        payment_url = "https://username.gitee.io/yourapp/payment.html"

    webbrowser.open(payment_url)
    messagebox.showinfo("提示", "支付页面已在浏览器中打开")

def open_qq_group():
    """打开QQ群"""
    import webbrowser
    try:
        from config import QQ_GROUP_URL
        qq_url = QQ_GROUP_URL
    except ImportError:
        qq_url = "https://qm.qq.com/cgi-bin/qm/qr?k=your_qq_group_key"

    webbrowser.open(qq_url)
    messagebox.showinfo("提示", "QQ群链接已在浏览器中打开")

def open_email_contact():
    """打开邮箱联系"""
    import webbrowser
    try:
        from config import CONTACT_EMAIL
        email = CONTACT_EMAIL
    except ImportError:
        email = "<EMAIL>"

    webbrowser.open(f"mailto:{email}")
    messagebox.showinfo("提示", f"邮箱客户端已打开，收件人：{email}")

def open_official_website():
    """打开官方网站"""
    import webbrowser
    try:
        from config import OFFICIAL_WEBSITE_URL
        website_url = OFFICIAL_WEBSITE_URL
    except ImportError:
        website_url = "https://yourapp.com"

    webbrowser.open(website_url)
    messagebox.showinfo("提示", "官方网站已在浏览器中打开")

def show_activation_dialog(license_manager):
    """显示激活对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 设置窗口图标
    try:
        set_window_icon_safe(root)
    except:
        pass

    # 创建自定义激活选择对话框
    choice = show_activation_choice_dialog(root)

    if choice == "activate":
        # 用户选择直接输入激活码
        activate_with_code(root, license_manager)
    elif choice == "get_code":
        # 用户选择获取激活码
        show_full_activation_dialog(root, license_manager)
        # 等待激活窗口操作完成
        root.mainloop()
    else:
        # 用户选择退出
        root.destroy()
        print("用户取消激活，程序退出")

def start_main_application():
    """启动主应用程序"""
    print("正在启动批量文件重命名工具...")

    try:
        # 尝试使用完整的GUI模块
        app = BatchRenameGUI()
    except NameError:
        # 如果模块未导入，使用简单版本
        app = SimpleBatchRenameGUI()

    app.run()

def main():
    """主函数"""
    check_license_and_start()

if __name__ == "__main__":
    main()
