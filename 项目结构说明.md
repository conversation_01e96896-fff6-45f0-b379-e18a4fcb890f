# 项目结构说明

## 文件分类

### 🟢 用户端文件（打包到exe）
这些文件会被打包到最终的exe文件中，用户会获得：

```
用户端文件/
├── main.py                    # 主程序入口（含许可证验证）
├── gui_module.py              # GUI界面模块
├── license_manager.py         # 许可证管理器
├── config.py                  # 用户端配置（无敏感信息）
├── rename_engine.py           # 重命名引擎
├── file_reader.py             # 文件读取模块
├── pattern_generator.py       # 规律生成器
├── requirements.txt           # 依赖包列表
└── 使用说明.md               # 用户使用说明
```

### 🔴 开发者专用文件（不打包）
这些文件仅供开发者使用，**绝对不能**打包给用户：

```
开发者工具/
├── activation_code_manager.py  # 激活码管理工具
├── cloud_function.py          # 云函数代码
├── admin_config.py            # 管理员配置（含敏感信息）
├── build_release.py           # 发布构建脚本
└── 项目结构说明.md           # 本文档
```

## 安全考虑

### ✅ 安全的做法：
- 用户端只包含必要的功能代码
- 配置文件不含管理员密钥
- 激活码生成算法不暴露给用户
- 管理工具与用户端完全分离

### ❌ 危险的做法：
- 把激活码管理工具打包给用户
- 在用户端配置中包含管理员密钥
- 在用户端代码中硬编码激活码
- 把云函数代码打包给用户

## 构建流程

### 1. 开发阶段
```bash
# 在完整项目目录中开发和测试
python main.py                    # 测试主程序
python activation_code_manager.py # 管理激活码
```

### 2. 发布阶段
```bash
# 运行构建脚本
python build_release.py

# 构建脚本会：
# 1. 创建release目录
# 2. 只复制用户端文件
# 3. 验证安全性
# 4. 构建exe文件
# 5. 创建发布包
```

### 3. 部署阶段
```bash
# 部署云函数
# 1. 将cloud_function.py部署到腾讯云函数
# 2. 配置环境变量ADMIN_SECRET_KEY
# 3. 创建API网关触发器
# 4. 获取API地址并更新config.py

# 生成激活码
python activation_code_manager.py
# 1. 输入管理员密钥
# 2. 生成激活码
# 3. 上传到云函数
```

## 文件内容说明

### config.py（用户端）
```python
# ✅ 包含的内容：
CLOUD_FUNCTION_URL = "https://..."  # API地址
PAYMENT_PAGE_URL = "https://..."    # 支付页面
SOFTWARE_NAME = "..."               # 软件名称

# ❌ 不包含的内容：
# ADMIN_SECRET_KEY                  # 管理员密钥
# 激活码生成算法                    # 敏感算法
# 数据库连接信息                    # 敏感信息
```

### admin_config.py（开发者专用）
```python
# 包含所有敏感信息：
ADMIN_SECRET_KEY = "..."           # 管理员密钥
DB_PASSWORD = "..."                # 数据库密码
EMAIL_PASSWORD = "..."             # 邮箱密码
```

## 许可证验证流程

### 用户端流程：
```
1. 用户启动软件
2. license_manager.py 获取设备指纹
3. 向云函数发送验证请求
4. 云函数返回试用次数/激活状态
5. 根据结果决定是否允许使用
```

### 管理员流程：
```
1. 运行activation_code_manager.py
2. 生成激活码并保存本地备份
3. 通过管理员API上传到云函数
4. 用户购买后分配激活码
5. 用户输入激活码完成激活
```

## 部署检查清单

### 云函数部署：
- [ ] 部署cloud_function.py到腾讯云函数
- [ ] 设置环境变量ADMIN_SECRET_KEY
- [ ] 创建API网关触发器
- [ ] 测试API接口可用性

### 用户端配置：
- [ ] 更新config.py中的CLOUD_FUNCTION_URL
- [ ] 更新支付页面等URL
- [ ] 确认config.py不含敏感信息

### 激活码管理：
- [ ] 运行activation_code_manager.py
- [ ] 生成足够数量的激活码
- [ ] 上传激活码到云函数
- [ ] 保存本地备份文件

### 发布构建：
- [ ] 运行build_release.py
- [ ] 验证exe文件正常运行
- [ ] 测试许可证验证功能
- [ ] 测试激活功能

## 注意事项

1. **绝对不要**把管理员工具打包给用户
2. **定期更换**管理员密钥
3. **备份**所有生成的激活码
4. **测试**每个发布版本的功能
5. **监控**云函数的使用情况和费用
